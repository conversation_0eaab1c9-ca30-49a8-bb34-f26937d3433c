/* إصلاح السايد بار toggle في العربية */
:host-context(html[dir="rtl"]) #kt_app_sidebar_toggle,
:host-context(html[lang="ar"]) #kt_app_sidebar_toggle {
  left: 0 !important;
  right: auto !important;
  transform: translateX(-50%) translateY(-50%) !important;
}

:host-context(html[dir="rtl"]) .app-sidebar-toggle,
:host-context(html[lang="ar"]) .app-sidebar-toggle {
  left: 0 !important;
  right: auto !important;
}

:host-context(html[dir="rtl"]) .app-sidebar-toggle app-keenicon,
:host-context(html[lang="ar"]) .app-sidebar-toggle app-keenicon {
  transform: rotate(0deg) !important;
}

:host-context(html[dir="rtl"]) .ki-double-left,
:host-context(html[lang="ar"]) .ki-double-left {
  transform: rotate(0deg) !important;
}

// Additional mobile RTL fixes
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]) {
  // Fix sidebar positioning on mobile
  @media (max-width: 991.98px) {
    .app-sidebar {
      right: 0 !important;
      left: auto !important;
      transform: translateX(100%) !important;

      &.drawer-on {
        transform: translateX(0) !important;
      }
    }

    // Fix sidebar overlay
    .drawer-overlay {
      right: 0 !important;
      left: auto !important;
    }
  }

  // Fix sidebar content direction
  .app-sidebar {
    direction: rtl;

    .app-sidebar-logo {
      direction: rtl;
      text-align: right;
    }

    .app-sidebar-menu {
      direction: rtl;
    }

    .app-sidebar-footer {
      direction: rtl;
      text-align: right;
    }
  }
}
