<!-- Main Container with Padding -->
<div class="container-fluid px-3 px-md-4 px-lg-5">
  <!-- Header -->
  <div class="mb-5 mt-0">
    <app-developer-header [title]="
        (unitDetails?.type || 'unknown') +
        ' - ' +
        (unitDetails?.modelCode || 'unknown unit code')
      " [subtitle]="
        (unitDetails?.city?.name_en || 'unknown') +
        ', ' +
        (unitDetails?.area?.name_en || '')
      ">
    </app-developer-header>
  </div>

  <!-- Unit Details Content -->
  <div class="row">
    <!-- Left Column - Main Image and Gallery -->
    <div class="col-lg-8">
      <!-- Main Unit Image -->
      <div class="card mb-5">
        <div class="card-body p-0">
          <div class="position-relative unit-image-container">
            <!-- Background with gradient -->
            <div class="unit-image-background">
              <!-- Bootstrap Carousel -->
              <div class="h-100">
                <div id="unitImagesCarousel" class="carousel slide h-100" data-bs-ride="carousel"
                  data-bs-interval="3000">
                  <!-- Carousel Inner -->
                  <div class="carousel-inner h-100">
                    <!-- <div class="carousel-item h-100" *ngFor="let image of unitImages; let i = index"
                    [class.active]="i === 0">
                    <img [src]="image.url || 'assets/media/auth/404-error.png'" [alt]="'Unit Image ' + (i + 1)"
                      class="d-block w-100 h-100 cursor-pointer" style="object-fit: cover; border-radius: 0.5rem" />
                  </div> -->

                    <!-- Gallery Items -->
                    <div class="carousel-item h-100" *ngFor="let item of AllImages; let i = index"
                      [class.active]="i === 0">
                      <ng-container *ngIf="item?.type === 'image'">
                        <img [src]="item?.url || 'assets/media/auth/404-error.png'" [alt]="'Unit Image ' + (i + 1)"
                          class="d-block w-100 h-100 cursor-pointer" style="object-fit: cover; border-radius: 0.5rem" />
                      </ng-container>
                      <ng-container *ngIf="item?.type === 'video'">
                        <video [src]="item?.url" controls class="d-block w-100 h-100 cursor-pointer"
                          style="object-fit: cover; border-radius: 0.5rem"></video>
                      </ng-container>
                    </div>
                    <!-- <div class="carousel-item h-100" *ngIf="unitDetails?.diagram">
                    <img [src]="unitDetails?.diagram || 'assets/media/auth/404-error.png'" [alt]="'Unit Diagram'"
                      class="d-block w-100 h-100 cursor-pointer" style="object-fit: cover; border-radius: 0.5rem" />
                  </div> -->
                    <!-- Location in Master Plan Item -->
                    <!-- <div class="carousel-item h-100" *ngIf="unitDetails?.locationInMasterPlan">
                    <img [src]=" unitDetails?.locationInMasterPlan|| 'assets/media/auth/404-error.png'"
                      alt="Location in Master Plan" class="d-block w-100 h-100 cursor-pointer"
                      style="object-fit: cover; border-radius: 0.5rem" />
                  </div> -->
                  </div>

                  <!-- Carousel Indicators -->
                  <div class="carousel-indicators">
                    <button type="button" data-bs-target="#unitImagesCarousel"
                      *ngFor="let image of AllImages; let i = index" [attr.data-bs-slide-to]="i"
                      [class.active]="i === 0"></button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Status Badge -->
            <div class="position-absolute top-0 start-0 m-3">
              <span class="badge bg-pink text-white fs-7 px-3 py-2 rounded-pill">
                {{ unitDetails?.type || "unknown" }}
              </span>
            </div>

            <!-- Unit Information Overlay -->
            <div class="position-absolute bottom-0 start-0 end-0 unit-info-overlay">
              <div class="p-4">
                <div class="d-flex align-items-center mb-2">
                  <h2 class="text-white fw-bold mb-0 me-3">
                    {{ unitDetails?.modelCode || "unknown unit code" }} -
                    {{ unitDetails?.numberOfRooms || "unknown" }} Rooms
                  </h2>
                  <i class="fa-solid fa-check-circle text-success fa-lg"></i>
                </div>
                <p class="text-white mb-0">
                  <i class="fa-solid fa-location-dot me-2"></i>
                  {{ unitDetails?.city?.name_en || "unknown" }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Property Details Section -->
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Property details</h5>
        </div>
        <div class="card-body">
          <div class="property-details-text">
            <p class="mb-3 fs-5">
              This is a <strong>{{ unitDetails?.type }}</strong> unit located in
              <strong>{{ unitDetails?.area?.name_en }},
                {{ unitDetails?.city?.name_en }}</strong>. The unit is situated in building number
              <strong>{{ unitDetails?.buildingNumber }}</strong> on the
              <strong>{{ unitDetails?.floor }}</strong> floor.

              <span *ngIf="unitDetails?.unitArea">It spans an area of
                <strong>{{ unitDetails?.unitArea | number : "1.0-0" }} m²</strong>.</span>

              The unit features
              <strong>{{ unitDetails?.finishingType }}</strong> finishing and is
              available with
              <strong>{{ unitDetails?.paymentSystem }}</strong> payment options.

              <span *ngIf="unitDetails?.pricePerMeterInCash">The price per meter in cash is
                <strong>{{
                  unitDetails?.pricePerMeterInCash | number : "1.0-0"
                  }}
                  EGP</strong>,</span>
              <span *ngIf="unitDetails?.pricePerMeterInInstallment">
                while the price per meter in installments is
                <strong>{{
                  unitDetails?.pricePerMeterInInstallment | number : "1.0-0"
                  }}
                  EGP</strong>.</span>

              <span *ngIf="unitDetails?.totalPriceInCash">The total cash price is
                <strong>{{
                  unitDetails?.totalPriceInCash | number : "1.0-0"
                  }}
                  EGP</strong>,</span>
              <span *ngIf="unitDetails?.totalPriceInInstallment">
                and the total installment price is
                <strong>{{
                  unitDetails?.totalPriceInInstallment | number : "1.0-0"
                  }}
                  EGP</strong>.</span>
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Column - Unit Information -->
    <div class="col-lg-4">
      <!-- Price Card -->
      <div class="card mb-4">
        <div class="card-body text-center">
          <h3 class="text-success fw-bold mb-2">
            {{ unitDetails?.totalPriceInCash | number : "1.0-0" }} EGP
          </h3>
          <div class="d-flex justify-content-center gap-2 mb-3">
            <span class="badge bg-light-primary text-primary">Compound</span>
            <span class="badge bg-light-success text-success">{{
              unitDetails?.status
              }}</span>
          </div>
        </div>
      </div>

      <!-- Unit Information -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">Unit Information</h5>
        </div>
        <div class="card-body">
          <div class="row g-3">
            <div class="col-6">
              <div class="d-flex align-items-center">
                <i class="fa-solid fa-bed text-primary me-2"></i>
                <div>
                  <small class="text-muted d-block">Bedrooms</small>
                  <span class="fw-semibold">{{
                    unitDetails?.numberOfRooms
                    }}</span>
                </div>
              </div>
            </div>
            <div class="col-6">
              <div class="d-flex align-items-center">
                <i class="fa-solid fa-bath text-primary me-2"></i>
                <div>
                  <small class="text-muted d-block">Bathrooms</small>
                  <span class="fw-semibold">{{
                    unitDetails?.numberOfBathrooms
                    }}</span>
                </div>
              </div>
            </div>
            <div class="col-6">
              <div class="d-flex align-items-center">
                <i class="fa-solid fa-ruler-combined text-primary me-2"></i>
                <div>
                  <small class="text-muted d-block">Area</small>
                  <span class="fw-semibold">{{ unitDetails?.unitArea }} m²</span>
                </div>
              </div>
            </div>
            <div class="col-6">
              <div class="d-flex align-items-center">
                <i class="fa-solid fa-building text-primary me-2"></i>
                <div>
                  <small class="text-muted d-block">Floor</small>
                  <span class="fw-semibold">{{ (unitDetails?.floor).slice(0, 14)}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Features -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">Features</h5>
        </div>
        <div class="card-body">
          <div class="row g-2">
            <div class="col-12" *ngFor="let feature of features">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" [checked]="isFeatureEnabled(feature.value)" disabled />
                <label class="form-check-label text-dark">{{
                  feature.name
                  }}</label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>