import { Injectable } from '@angular/core';
import { Validators, ValidatorFn } from '@angular/forms';
import {
  FLOOR_TYPES_OPTIONS,
  BUILDING_LICENSE_TYPES_OPTIONS,
  UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
  BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
  GROUND_LAYOUT_STATUS_TYPES_OPTIONS,
  UNIT_DESIGN_TYPES_OPTIONS,
  ACTIVITY_TYPES_OPTIONS,
  UNIT_DESCRIPTION_TYPES_OPTIONS,
  SUB_UNIT_TYPE_OPTIONS,
  RENT_RECURRENCE_OPTIONS,
  PAYMENT_METHOD_OPTIONS,
  REQUIRED_INSURANCE_TYPES_OPTIONS,
  OTHER_EXPENSES_OPTIONS,
  FURNISHING_STATUS_OPTIONS,
  DELIVERY_STATUS_TYPES_OPTIONS,
  FINANCIAL_STATUS_TYPES_OPTIONS,
  LEGAL_STATUS_TYPES_OPTIONS,
  FIT_OUT_CONDITION_TYPES_OPTIONS,
  FINISHING_STATUS_TYPES_OPTIONS,
  UNIT_VIEW_TYPES_OPTIONS,
  UNIT_FACING_TYPES_OPTIONS,
  OTHER_ACCESSORIES_OPTIONS,
  BUILDING_DEADLINE_TYPES_OPTIONS,
  OptionItem,
  PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
  PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
  PURCHASE_FINISHING_STATUS_TYPES_OPTIONS
} from '../stepper-modal.constants';

export interface InputConfig {
  step: number;
  name: string;
  type: 'text' | 'number' | 'select' | 'multiSelect' | 'checkbox' | 'file' | 'textarea' | 'url' | 'date';
  label: string;
  options?: OptionItem[];
  validators?: ValidatorFn[];
  visibility: () => boolean;
  isDynamic?: boolean;
}

export interface StepperConfiguration {
  key: string;
  value: InputConfig[];
}

@Injectable({
  providedIn: 'root'
})
export abstract class BaseConfigService {

  /**
   * Helper function to create common location inputs for step 2
   */
  protected createLocationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 2,
        name: 'compoundName',
        type: 'text',
        label: 'Compound Name',
        validators: [Validators.required],
        visibility: () => stepperModal.getInsideCompoundPrivilege(),
      },
      {
        step: 2,
        name: 'locationSuggestions',
        type: 'checkbox',
        label: 'Location Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 2,
        name: 'cityId',
        type: 'select',
        label: 'City',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'areaId',
        type: 'select',
        label: 'Area',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'subAreaId',
        type: 'select',
        label: 'Sub Area',
        options: [],
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Helper function to create common unit information inputs for step 3
   */
  protected createUnitInformationInputs(stepperModal: any, includeRooms: boolean = true): InputConfig[] {
    const baseInputs: InputConfig[] = [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Unit Area Min (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Unit Area Max (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
    ];

    // Add room inputs only for certain unit types
    if (includeRooms) {
      baseInputs.push(
        {
          step: 3,
          name: 'rooms',
          type: 'number',
          label: 'Number of Rooms',
          validators: [Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'bathRooms',
          type: 'number',
          label: 'Number of Bathrooms',
          validators: [Validators.min(0)],
          visibility: () => true,
        }
      );
    }

    // Add common finishing and delivery inputs
    baseInputs.push(
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      }
    );

    return baseInputs;
  }

  /**
   * Helper function to create penthouse-specific unit information inputs for step 3
   */
  protected createPenthouseUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Unit Area Min (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Unit Area Max (m²)',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Helper function to create villa-specific unit information inputs for step 3
   */
  protected createVillaUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Unit Area Min (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Unit Area Max (m²)',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Helper function to create house-type unit information inputs for step 3
   */
  protected createHouseUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Unit Area Min (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Unit Area Max (m²)',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'buildingAreaMin',
        type: 'number',
        label: 'Building Area Min (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingAreaMax',
        type: 'number',
        label: 'Building Area Max (m²)',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingAreaSuggestions',
        type: 'checkbox',
        label: 'Building Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Helper function to create commercial unit information inputs for step 3
   */
  protected createCommercialUnitInformationInputs(stepperModal: any, includeFitOut: boolean = false): InputConfig[] {
    const baseInputs: InputConfig[] = [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Unit Area Min (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Unit Area Max (m²)',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
    ];

    // Add fitout condition for administrative units
    if (includeFitOut) {
      baseInputs.push({
        step: 3,
        name: 'fitOutCondition',
        type: 'select',
        label: 'Fitout Condition',
        options: FIT_OUT_CONDITION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      });
    }

    // Add common ending inputs
    baseInputs.push(
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      }
    );

    return baseInputs;
  }

  /**
   * Helper function to create shop unit information inputs for step 3
   */
  protected createShopUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Unit Area Min (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Unit Area Max (m²)',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'activity',
        type: 'select',
        label: 'Activity Status',
        options: ACTIVITY_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Helper function to create commercial administrative building unit information inputs for step 3
   */
  protected createCommercialAdminBuildingUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Unit Area Min (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Unit Area Max (m²)',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'buildingAreaMin',
        type: 'number',
        label: 'Building Area Min (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingAreaMax',
        type: 'number',
        label: 'Building Area Max (m²)',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingAreaSuggestions',
        type: 'checkbox',
        label: 'Building Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'activity',
        type: 'select',
        label: 'Activity Status',
        options: ACTIVITY_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Helper function to create common financial inputs for step 5
   */
  protected createFinancialInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 5,
        name: 'averageUnitPriceMin',
        type: 'number',
        label: 'Unit Price Min',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'averageUnitPriceMax',
        type: 'number',
        label: 'Unit Price Max',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'averageUnitPriceSuggestion',
        type: 'checkbox',
        label: 'Unit Price Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 5,
        name: 'paymentMethod',
        type: 'select',
        label: 'Payment Methods',
        options: PAYMENT_METHOD_OPTIONS,
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Helper function to create document inputs for step 4
   */
  protected createDocumentInputs(): InputConfig[] {
    return [
      {
        step: 4,
        name: 'mainImage',
        type: 'file',
        label: 'Main Image',
        validators: [],
        visibility: () => true,
      },
      {
        step: 4,
        name: 'galleryImages',
        type: 'file',
        label: 'Gallery Images',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Get default inputs for a step when no specific configuration is found
   */
  protected getDefaultInputsForStep(step: number): InputConfig[] {
    if (step === 2) {
      return [
        {
          step: 2,
          name: 'cityId',
          type: 'select',
          label: 'City',
          options: [],
          validators: [Validators.required],
          visibility: () => true,
        },
        {
          step: 2,
          name: 'areaId',
          type: 'select',
          label: 'Area',
          options: [],
          validators: [Validators.required],
          visibility: () => true,
        },
        {
          step: 2,
          name: 'subAreaId',
          type: 'select',
          label: 'Sub Area',
          options: [],
          validators: [],
          visibility: () => true,
        },
      ];
    }
    return [];
  }

  /**
   * Abstract method to get input configurations
   */
  abstract getInputConfigs(stepperModal: any): StepperConfiguration[];

  /**
   * Get inputs for a specific configuration key and step
   */
  getInputsForKey(key: string, step: number, stepperModal: any): InputConfig[] {
    console.log(key);
    const config = this.getInputConfigs(stepperModal).find((c) => c.key === key);
    return config
      ? config.value.filter((input) => input.step === step && input.visibility())
      : this.getDefaultInputsForStep(step);
  }

  /**
   * Check if a configuration key exists
   */
  hasConfiguration(key: string, stepperModal: any): boolean {
    return this.getInputConfigs(stepperModal).some(config => config.key === key);
  }
}
