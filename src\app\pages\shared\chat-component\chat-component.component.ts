import { ChangeDetectorRef, Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { CometChat } from '@cometchat-pro/chat';
import { ChatService } from '../services/chat.service';
declare var CometChatWidget: any;

@Component({
  selector: 'app-chat-component',
  templateUrl: './chat-component.component.html',
  styleUrls: ['./chat-component.component.scss']
})

export class ChatComponentComponent implements OnInit, OnDestroy {

  // session user
  UID :any;

 chatWithUID?: string;

  constructor(private chatService: ChatService ,private route: ActivatedRoute, private cd: ChangeDetectorRef) {}

  ngOnInit(): void {
    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;
    this.UID = user?.id;
    this.route.queryParams.subscribe(params => {
    this.chatWithUID = params['chatWithUID'] || undefined;});

    const appID = '277125e04ff598e9';
    const region = 'us';
    const authKey = '290ffb4caa0c1c1b6af3a55a08f2acc1180051ea';
    const widgetID = '42bd2ecc-1917-4988-bc03-fd9a996921e1';

    CometChatWidget.init({
      appID,
      appRegion: region,
      authKey
    }).then(() => {
      console.log('Widget initialized');

      CometChatWidget.login({
        uid: this.UID
      }).then(() => {
        console.log('Widget login successful');

        //notificatioon
        this.addMessageListener();

        this.launchWidget(widgetID);
      }, (loginError: any) => {
        console.error('Login failed', loginError);
      });

    }, (initError: any) => {
      console.error('Widget init failed', initError);
    });
  }

  ngOnDestroy(): void {
    CometChat.removeMessageListener('cometchat-listener');
  }

  launchWidget(widgetID: string): void {
    const config: any = {
      widgetID: widgetID,
      target: '#cometchat-container',
      roundedCorners: true,
      height: '600px',
      width: '100%',
      defaultType: 'user'
    };

    if (this.chatWithUID) {
      config.defaultID = this.chatWithUID;
    }

    CometChatWidget.launch(config);
  }

  addMessageListener(): void {
  const listenerID = 'cometchat-listener';

  CometChat.addMessageListener(
    listenerID,
    new CometChat.MessageListener({
      onTextMessageReceived: (message: any) => {
        console.log('New text message received:', message);

        this.chatService.increment();
        this.cd.detectChanges();

        //Display browser or toast notification
        alert(`📨 New message from ${message.sender.name}: ${message.text}`);
      },
      onMediaMessageReceived: (message: any) => {
        console.log('New media message received:', message);
        alert(`📸 New media message from ${message.sender.name}`);
      },
    })
  );
}

}

