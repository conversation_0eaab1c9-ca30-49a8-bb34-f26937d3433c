import { Injectable } from '@angular/core';
import { Validators } from '@angular/forms';
import { BaseConfigService, InputConfig, StepperConfiguration } from '../base-config.service';
import {
  UNIT_VIEW_TYPES_OPTIONS,
  UNIT_FACING_TYPES_OPTIONS,
  FINISHING_STATUS_TYPES_OPTIONS,
  FURNISHING_STATUS_OPTIONS,
  OTHER_ACCESSORIES_OPTIONS,
  RENT_RECURRENCE_OPTIONS,
  REQUIRED_INSURANCE_TYPES_OPTIONS,
  OTHER_EXPENSES_OPTIONS,
  FLOOR_TYPES_OPTIONS,
  UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
  BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
  UNIT_DESIGN_TYPES_OPTIONS,
  FIT_OUT_CONDITION_TYPES_OPTIONS,
  ACTIVITY_TYPES_OPTIONS,
  SUB_UNIT_OPTIONS,
  PURCHASE_VACATION_UNIT_VIEW_TYPES_OPTIONS,
  PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
  RENT_DURATION_OPTIONS,
  Hotels_SUB_UNIT_OPTIONS,
  SELL_FLOOR_TYPES_OPTIONS,
  SELL_VACATION_UNIT_VIEW_TYPES_OPTIONS,
  SPECIFIC_OTHER_ACCESSORIES_OPTIONS,
  RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
  PHARMACY_FINISHING_STATUS_TYPES_OPTIONS,
  Purchase_OTHER_ACCESSORIES_OPTIONS,
  BASEMENT_UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
  Purchase_ROOF_UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
  SELL_OUT_BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
  RENT_BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
  PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
  RENTALIN_FINISHING_STATUS_TYPES_OPTIONS,
  BUILD_RENT_FINISHING_STATUS_TYPES_OPTIONS,
} from '../../stepper-modal.constants';

@Injectable({
  providedIn: 'root'
})
export class RentalOutsideCompoundConfigService extends BaseConfigService {

  // ============================================================================
  // RENT-OUT CONFIGURATIONS (Property owner looking to rent out)
  // ============================================================================

  /**
   * Create rental-specific location inputs for rent-out outside compound scenarios
   */
  private createRentOutOutsideCompoundLocationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 2,
        name: 'cityId',
        type: 'select',
        label: 'City',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'areaId',
        type: 'select',
        label: 'Area',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'subAreaId',
        type: 'select',
        label: 'Sub Area',
        options: [],
        validators: [],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'detailedAddress',
        type: 'text',
        label: 'Detailed Address',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'addressLink',
        type: 'url',
        label: 'Address Link',
        validators: [Validators.pattern(/^https?:\/\/.+/)],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rental-specific location inputs for rent-out outside compound administrative units (includes mallName)
   */
  private createRentOutOutsideCompoundAdministrativeUnitsLocationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 2,
        name: 'cityId',
        type: 'select',
        label: 'City',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'areaId',
        type: 'select',
        label: 'Area',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'subAreaId',
        type: 'select',
        label: 'Sub Area',
        options: [],
        validators: [],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'detailedAddress',
        type: 'text',
        label: 'Detailed Address',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'addressLink',
        type: 'url',
        label: 'Address Link',
        validators: [Validators.pattern(/^https?:\/\/.+/)],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'mallName',
        type: 'text',
        label: 'Mall Name',
        validators: [Validators.required],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out outside compound unit information inputs for apartments and duplexes
   */
  private createRentOutOutsideCompoundUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitFacing',
        type: 'select',
        label: 'Unit Facing',
        options: UNIT_FACING_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out outside compound unit information inputs for studios (without rooms)
   */
  private createRentOutOutsideCompoundStudiosUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
       {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitFacing',
        type: 'select',
        label: 'Unit Facing',
        options: UNIT_FACING_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out outside compound unit information inputs for penthouses (without floor)
   */
  private createRentOutOutsideCompoundPenthousesUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitFacing',
        type: 'select',
        label: 'Unit Facing',
        options: UNIT_FACING_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out outside compound unit information inputs for basements
   */
  private createRentOutOutsideCompoundBasementsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitLayoutStatus',
        type: 'select',
        label: 'Unit Layout Status',
        options: BASEMENT_UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out outside compound unit information inputs for roofs
   */
  private createRentOutOutsideCompoundRoofsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitLayoutStatus',
        type: 'select',
        label: 'Unit Layout Status',
        options: Purchase_ROOF_UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingLayoutStatus',
        type: 'select',
        label: 'Building Layout Status',
        options: SELL_OUT_BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out outside compound unit information inputs for standalone villas
   */
  private createRentOutOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitDesign',
        type: 'select',
        label: 'Unit Design',
        options: UNIT_DESIGN_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundArea',
        type: 'number',
        label: 'Ground Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingArea',
        type: 'number',
        label: 'Building Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out outside compound unit information inputs for administrative units
   */
  private createRentOutOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
       {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out outside compound unit information inputs for medical clinics
   */
  private createRentOutOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out outside compound unit information inputs for pharmacies
   */
  private createRentOutOutsideCompoundPharmaciesUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PHARMACY_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'fitOutCondition',
        type: 'select',
        label: 'Fit Out Condition',
        options: FIT_OUT_CONDITION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out outside compound unit information inputs for commercial stores
   */
  private createRentOutOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PHARMACY_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'fitOutCondition',
        type: 'select',
        label: 'Fit Out Condition',
        options: FIT_OUT_CONDITION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'shopActivity',
        type: 'text',
        label: 'Activity',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out outside compound unit information inputs for industrial properties
   * (factory_lands, factories, warehouses, warehouse_lands)
   */
  private createRentOutOutsideCompoundIndustrialUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundArea',
        type: 'number',
        label: 'Ground Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingArea',
        type: 'number',
        label: 'Building Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingLayoutStatus',
        type: 'select',
        label: 'Building Layout Status',
        options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'fitOutCondition',
        type: 'select',
        label: 'Fit Out Condition',
        options: FIT_OUT_CONDITION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'activity',
        type: 'select',
        label: 'Activity Type',
        options: ACTIVITY_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out outside compound unit information inputs for commercial administrative buildings
   */
  private createRentOutOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundArea',
        type: 'number',
        label: 'Ground Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingArea',
        type: 'number',
        label: 'Building Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingLayoutStatus',
        type: 'select',
        label: 'Building Layout Status',
        options: RENT_BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PHARMACY_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'activity',
        type: 'select',
        label: 'Activity Type',
        options: ACTIVITY_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  private createRentOutOutsideCompoundVacationVillasInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'villageName',
        type: 'text',
        label: 'Village Name',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'subUnitType',
        type: 'select',
        label: 'SubUnit Type',
        options: SUB_UNIT_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: SELL_VACATION_UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitFacing',
        type: 'select',
        label: 'Unit Facing',
        options: UNIT_FACING_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
       {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: SPECIFIC_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  private createRentOutOutsideChaletsInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'villageName',
        type: 'text',
        label: 'Village Name',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'subUnitType',
        type: 'select',
        label: 'SubUnit Type',
        options: SUB_UNIT_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: SELL_VACATION_UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitFacing',
        type: 'select',
        label: 'Unit Facing',
        options: UNIT_FACING_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
       {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: SPECIFIC_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  private createRentOutOutsideHotelsInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'subUnitType',
        type: 'select',
        label: 'SubUnit Type',
        options: Hotels_SUB_UNIT_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: SELL_VACATION_UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitFacing',
        type: 'select',
        label: 'Unit Facing',
        options: UNIT_FACING_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
       {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: SPECIFIC_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out outside compound financial inputs
   */
  private createRentOutOutsideCompoundFinancialInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 5,
        name: 'unitPrice',
        type: 'number',
        label: 'Monthly Rent',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'unitPriceSuggestions',
        type: 'checkbox',
        label: 'Monthly Rent Suggestions',
        validators: [],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'rentRecurrence',
        type: 'select',
        label: 'Rent Recurrence',
        options: RENT_RECURRENCE_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'requiredInsurance',
        type: 'select',
        label: 'Required Insurance',
        options: REQUIRED_INSURANCE_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'requiredInsuranceValue',
        type: 'text',
        label: 'Required Insurance Value',
        validators: [],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'otherExpenses',
        type: 'multiSelect',
        label: 'Other Expenses (Tenant Responsibility)',
        options: OTHER_EXPENSES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
    ];
  }

  private createRentOutOutsideCompoundSpecificFinancialInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 5,
        name: 'unitPriceMonthly',
        type: 'number',
        label: 'Monthly Unit Price',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'unitPriceMonthlySuggestions',
        type: 'checkbox',
        label: 'Monthly Unit Price Suggestions',
        validators: [],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'unitPriceDaily',
        type: 'number',
        label: 'Daily Unit Price',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'unitPriceDailySuggestions',
        type: 'checkbox',
        label: 'Daily Unit Price Suggestions',
        validators: [],
        visibility: () => true,
      },
    ];
  }


  /**
   * Create media inputs for step 4
   */
  private createMediaInputs(): InputConfig[] {
    return [
      {
        step: 4,
        name: 'mainImage',
        type: 'file',
        label: 'Main Image',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 4,
        name: 'galleryImages',
        type: 'file',
        label: 'Gallery Images',
        validators: [],
        visibility: () => true,
      },
      {
        step: 4,
        name: 'video',
        type: 'file',
        label: 'Video',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  // ============================================================================
  // SPECIFIC CONFIGURATIONS
  // ============================================================================

  private createRentOutOutsideCompoundApartmentsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentOutOutsideCompoundUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutOutsideCompoundDuplexesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentOutOutsideCompoundUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutOutsideCompoundStudiosConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentOutOutsideCompoundStudiosUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutOutsideCompoundPenthousesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentOutOutsideCompoundPenthousesUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutOutsideCompoundBasementsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentOutOutsideCompoundBasementsUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutOutsideCompoundRoofsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentOutOutsideCompoundRoofsUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutOutsideCompoundStandaloneVillasConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentOutOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutOutsideCompoundAdministrativeUnitsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundAdministrativeUnitsLocationInputs(stepperModal),
      ...this.createRentOutOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutOutsideCompoundMedicalClinicsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundAdministrativeUnitsLocationInputs(stepperModal),
      ...this.createRentOutOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutOutsideCompoundPharmaciesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundAdministrativeUnitsLocationInputs(stepperModal),
      ...this.createRentOutOutsideCompoundPharmaciesUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutOutsideCompoundCommercialStoresConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundAdministrativeUnitsLocationInputs(stepperModal),
      ...this.createRentOutOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutOutsideCompoundFactoryLandsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentOutOutsideCompoundIndustrialUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutOutsideCompoundFactoriesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentOutOutsideCompoundIndustrialUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutOutsideCompoundWarehousesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentOutOutsideCompoundIndustrialUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutOutsideCompoundWarehouseLandsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentOutOutsideCompoundIndustrialUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentOutOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutOutsideCompoundVacationVillasConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentOutOutsideCompoundVacationVillasInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundSpecificFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutOutsideCompoundChaletsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentOutOutsideChaletsInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundSpecificFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutOutsideCompoundHotelsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentOutOutsideHotelsInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutOutsideCompoundSpecificFinancialInputs(stepperModal));

    return config;
  }

  // ============================================================================
  // RENT-IN CONFIGURATIONS (Property seeker looking to rent)
  // ============================================================================

  /**
   * Create rental-specific location inputs for rent-in outside compound scenarios
   */
  private createRentInOutsideCompoundLocationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 2,
        name: 'cityId',
        type: 'select',
        label: 'City',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'areaId',
        type: 'select',
        label: 'Area',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'subAreaId',
        type: 'select',
        label: 'Sub Area',
        options: [],
        validators: [],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'locationSuggestions',
        type: 'checkbox',
        label: 'Location Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
    ];
  }

  /**
   * Create rent-in outside compound unit information inputs for apartments and duplexes
   */
  private createRentInOutsideCompoundUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor Number',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Minimum Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Maximum Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: RENTALIN_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-in outside compound financial inputs
   */
  private createRentInOutsideCompoundFinancialInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 5,
        name: 'averageUnitPriceMin',
        type: 'number',
        label: 'Minimum Average Monthly Rent',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'averageUnitPriceMax',
        type: 'number',
        label: 'Maximum Average Monthly Rent',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'unitPriceSuggestions',
        type: 'checkbox',
        label: 'Monthly Rent Suggestions',
        validators: [],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'rentRecurrence',
        type: 'select',
        label: 'Rent Recurrence',
        options: RENT_RECURRENCE_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
    ];
  }

  private createRentInOutsideCompoundSpecificFinancialInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 5,
        name: 'averageUnitPriceMonthlyMin',
        type: 'number',
        label: 'Minimum Monthly Average Unit Price',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => stepperModal.isMonthly(),
      },
      {
        step: 5,
        name: 'averageUnitPriceMonthlyMax',
        type: 'number',
        label: 'Maximum Monthly Average Unit Price',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => stepperModal.isMonthly(),
      },
      {
        step: 5,
        name: 'averageUnitPriceMonthlySuggestions',
        type: 'checkbox',
        label: 'Monthly Unit Price Suggestions',
        validators: [],
        visibility: () => stepperModal.isMonthly(),
      },
      {
        step: 5,
        name: 'averageUnitPriceDailyMin',
        type: 'number',
        label: 'Minimum Daily Average Unit Price',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => !stepperModal.isMonthly(),
      },
      {
        step: 5,
        name: 'averageUnitPriceDailyMax',
        type: 'number',
        label: 'Maximum Daily Average Unit Price',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => !stepperModal.isMonthly(),
      },
      {
        step: 5,
        name: 'averageUnitPriceDailySuggestions',
        type: 'checkbox',
        label: 'Daily Unit Price Suggestions',
        validators: [],
        visibility: () => !stepperModal.isMonthly(),
      },
    ];
  }

  /**
   * Configuration for rent-in outside compound apartments
   */
  private createRentInOutsideCompoundApartmentsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentInOutsideCompoundUnitInformationInputs(stepperModal),
    ];

    // No media inputs for step 4 in rent-in scenarios

    // Add financial inputs for step 5
    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  /**
   * Configuration for rent-in outside compound duplexes
   */
  private createRentInOutsideCompoundDuplexesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentInOutsideCompoundUnitInformationInputs(stepperModal),
    ];

    // No media inputs for step 4 in rent-in scenarios

    // Add financial inputs for step 5
    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  /**
   * Create rent-in outside compound unit information inputs for studios
   */
  private createRentInOutsideCompoundStudiosUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor Number',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Minimum Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Maximum Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Configuration for rent-in outside compound studios
   */
  private createRentInOutsideCompoundStudiosConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentInOutsideCompoundStudiosUnitInformationInputs(stepperModal),
    ];

    // No media inputs for step 4 in rent-in scenarios

    // Add financial inputs for step 5
    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  /**
   * Create rent-in outside compound unit information inputs for penthouses
   */
  private createRentInOutsideCompoundPenthousesUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Minimum Unit Area (m²)',
        validators: [Validators.required, Validators.min(0.01)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Maximum Unit Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Configuration for rent-in outside compound penthouses
   */
  private createRentInOutsideCompoundPenthousesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentInOutsideCompoundPenthousesUnitInformationInputs(stepperModal),
    ];

    // No media inputs for step 4 in rent-in scenarios

    // Add financial inputs for step 5
    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  /**
   * Create rent-in outside compound unit information inputs for basements
   */
  private createRentInOutsideCompoundBasementsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Minimum Unit Area (m²)',
        validators: [Validators.required, Validators.min(0.01)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Maximum Unit Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitLayoutStatus',
        type: 'select',
        label: 'Unit Layout Status',
        options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Configuration for rent-in outside compound basements
   */
  private createRentInOutsideCompoundBasementsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentInOutsideCompoundBasementsUnitInformationInputs(stepperModal),
    ];

    // No media inputs for step 4 in rent-in scenarios

    // Add financial inputs for step 5
    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  /**
   * Create rent-in outside compound unit information inputs for roofs
   */
  private createRentInOutsideCompoundRoofsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Minimum Unit Area (m²)',
        validators: [Validators.required, Validators.min(0.01)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Maximum Unit Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitLayoutStatus',
        type: 'select',
        label: 'Unit Layout Status',
        options: Purchase_ROOF_UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingLayoutStatus',
        type: 'select',
        label: 'Building Layout Status',
        options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Configuration for rent-in outside compound roofs
   */
  private createRentInOutsideCompoundRoofsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentInOutsideCompoundRoofsUnitInformationInputs(stepperModal),
    ];

    // No media inputs for step 4 in rent-in scenarios

    // Add financial inputs for step 5
    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  /**
   * Create rent-in outside compound unit information inputs for standalone villas
   */
  private createRentInOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'groundAreaMin',
        type: 'number',
        label: 'Minimum Ground Area (m²)',
        validators: [Validators.required, Validators.min(0.01)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundAreaMax',
        type: 'number',
        label: 'Maximum Ground Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingAreaMin',
        type: 'number',
        label: 'Minimum Building Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingAreaMax',
        type: 'number',
        label: 'Maximum Building Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitDesign',
        type: 'select',
        label: 'Unit Design',
        options: UNIT_DESIGN_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Configuration for rent-in outside compound standalone villas
   */
  private createRentInOutsideCompoundStandaloneVillasConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentInOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal),
    ];

    // No media inputs for step 4 in rent-in scenarios

    // Add financial inputs for step 5
    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  /**
   * Create rent-in outside compound unit information inputs for administrative units
   */
  private createRentInOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Minimum Unit Area (m²)',
        validators: [Validators.required, Validators.min(0.01)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Maximum Unit Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-in outside compound unit information inputs for medical clinics
   */
  private createRentInOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Minimum Unit Area (m²)',
        validators: [Validators.required, Validators.min(0.01)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Maximum Unit Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Configuration for rent-in outside compound administrative units
   */
  private createRentInOutsideCompoundAdministrativeUnitsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentInOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal),
    ];

    // No media inputs for step 4 in rent-in scenarios

    // Add financial inputs for step 5
    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  /**
   * Configuration for rent-in outside compound medical clinics
   */
  private createRentInOutsideCompoundMedicalClinicsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentInOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal),
    ];

    // No media inputs for step 4 in rent-in scenarios

    // Add financial inputs for step 5
    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  /**
   * Create rent-in outside compound unit information inputs for pharmacies
   */
  private createRentInOutsideCompoundPharmaciesUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Minimum Unit Area (m²)',
        validators: [Validators.required, Validators.min(0.01)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Maximum Unit Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: BUILD_RENT_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'fitOutCondition',
        type: 'select',
        label: 'Fit Out Condition',
        options: FIT_OUT_CONDITION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Configuration for rent-in outside compound pharmacies
   */
  private createRentInOutsideCompoundPharmaciesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentInOutsideCompoundPharmaciesUnitInformationInputs(stepperModal),
    ];

    // No media inputs for step 4 in rent-in scenarios

    // Add financial inputs for step 5
    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  /**
   * Create rent-in outside compound unit information inputs for commercial stores
   */
  private createRentInOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Minimum Unit Area (m²)',
        validators: [Validators.required, Validators.min(0.01)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Maximum Unit Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: BUILD_RENT_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'fitOutCondition',
        type: 'select',
        label: 'Fit Out Condition',
        options: FIT_OUT_CONDITION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'shopActivity',
        type: 'text',
        label: 'Activity',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Configuration for rent-in outside compound commercial stores
   */
  private createRentInOutsideCompoundCommercialStoresConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentInOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal),
    ];

    // No media inputs for step 4 in rent-in scenarios

    // Add financial inputs for step 5
    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  /**
   * Create rent-in outside compound unit information inputs for industrial properties
   * (factory_lands, factories, warehouses, warehouse_lands, commercial_administrative_buildings)
   */
  private createRentInOutsideCompoundIndustrialUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'groundAreaMin',
        type: 'number',
        label: 'Minimum Ground Area (m²)',
        validators: [Validators.required, Validators.min(0.01)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundAreaMax',
        type: 'number',
        label: 'Maximum Ground Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingAreaMin',
        type: 'number',
        label: 'Minimum Building Area (m²)',
        validators: [Validators.required, Validators.min(0.01)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingAreaMax',
        type: 'number',
        label: 'Maximum Building Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingLayoutStatus',
        type: 'select',
        label: 'Building Layout Status',
        options: RENT_BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'fitOutCondition',
        type: 'select',
        label: 'Fit Out Condition',
        options: FIT_OUT_CONDITION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'shopActivity',
        type: 'text',
        label: 'Activity',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: BUILD_RENT_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }
  private createRentInOutsideCompoundCommericalAdminstrativeBuildingUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'groundAreaMin',
        type: 'number',
        label: 'Minimum Ground Area (m²)',
        validators: [Validators.required, Validators.min(0.01)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundAreaMax',
        type: 'number',
        label: 'Maximum Ground Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingAreaMin',
        type: 'number',
        label: 'Minimum Building Area (m²)',
        validators: [Validators.required, Validators.min(0.01)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingAreaMax',
        type: 'number',
        label: 'Maximum Building Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingLayoutStatus',
        type: 'select',
        label: 'Building Layout Status',
        options: RENT_BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'shopActivity',
        type: 'text',
        label: 'Activity',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: BUILD_RENT_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  private createRentInOutsideCompoundVacationVillasAndChalets(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'villageName',
        type: 'text',
        label: 'Village Name',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'subUnitType',
        type: 'select',
        label: 'SubUnit Type',
        options: SUB_UNIT_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rentDuration',
        type: 'select',
        label: 'Rent Duration',
        options: RENT_DURATION_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rentDateMin',
        type: 'date',
        label: 'Rent Date Min',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rentDateMax',
        type: 'date',
        label: 'Rent Date Max',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: PURCHASE_VACATION_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  private createRentInOutsideCompoundHotels(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'subUnitType',
        type: 'select',
        label: 'SubUnit Type',
        options: Hotels_SUB_UNIT_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rentDuration',
        type: 'select',
        label: 'Rent Duration',
        options: RENT_DURATION_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rentDateMin',
        type: 'date',
        label: 'Rent Date Min',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rentDateMax',
        type: 'date',
        label: 'Rent Date Max',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: PURCHASE_VACATION_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Configuration for rent-in outside compound factory lands
   */
  private createRentInOutsideCompoundFactoryLandsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentInOutsideCompoundIndustrialUnitInformationInputs(stepperModal),
    ];

    // No media inputs for step 4 in rent-in scenarios

    // Add financial inputs for step 5
    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  /**
   * Configuration for rent-in outside compound factories
   */
  private createRentInOutsideCompoundFactoriesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentInOutsideCompoundIndustrialUnitInformationInputs(stepperModal),
    ];

    // No media inputs for step 4 in rent-in scenarios

    // Add financial inputs for step 5
    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  /**
   * Configuration for rent-in outside compound warehouses
   */
  private createRentInOutsideCompoundWarehousesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentInOutsideCompoundIndustrialUnitInformationInputs(stepperModal),
    ];

    // No media inputs for step 4 in rent-in scenarios

    // Add financial inputs for step 5
    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  /**
   * Configuration for rent-in outside compound warehouse lands
   */
  private createRentInOutsideCompoundWarehouseLandsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentInOutsideCompoundIndustrialUnitInformationInputs(stepperModal),
    ];

    // No media inputs for step 4 in rent-in scenarios

    // Add financial inputs for step 5
    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  /**
   * Configuration for rent-in outside compound commercial administrative buildings
   */
  private createRentInOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentInOutsideCompoundCommericalAdminstrativeBuildingUnitInformationInputs(stepperModal),
    ];

    // No media inputs for step 4 in rent-in scenarios

    // Add financial inputs for step 5
    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));

    return config;
  }

  private createRentInOutsideCompoundVacationVillasAndChaletsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentInOutsideCompoundVacationVillasAndChalets(stepperModal),
    ];

    // No media inputs for step 4 in rent-in scenarios

    // Add financial inputs for step 5
    config.push(...this.createRentInOutsideCompoundSpecificFinancialInputs(stepperModal));

    return config;
  }

  private createRentInOutsideCompoundHotelsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),
      ...this.createRentInOutsideCompoundHotels(stepperModal),
    ];

    // No media inputs for step 4 in rent-in scenarios

    // Add financial inputs for step 5
    config.push(...this.createRentInOutsideCompoundSpecificFinancialInputs(stepperModal));

    return config;
  }

  // ============================================================================
  // PUBLIC API
  // ============================================================================

  /**
   * Get input configurations for rental outside compound cases
   */
  getInputConfigs(stepperModal: any): StepperConfiguration[] {
    return [
      // RENT-OUT OUTSIDE COMPOUND CONFIGURATIONS
      {
        key: 'rentals_outside_compound_rent_out_apartments',
        value: this.createRentOutOutsideCompoundApartmentsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_out_duplexes',
        value: this.createRentOutOutsideCompoundDuplexesConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_out_studios',
        value: this.createRentOutOutsideCompoundStudiosConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_out_penthouses',
        value: this.createRentOutOutsideCompoundPenthousesConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_out_basements',
        value: this.createRentOutOutsideCompoundBasementsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_out_roofs',
        value: this.createRentOutOutsideCompoundRoofsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_out_standalone_villas',
        value: this.createRentOutOutsideCompoundStandaloneVillasConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_out_administrative_units',
        value: this.createRentOutOutsideCompoundAdministrativeUnitsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_out_medical_clinics',
        value: this.createRentOutOutsideCompoundMedicalClinicsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_out_pharmacies',
        value: this.createRentOutOutsideCompoundPharmaciesConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_out_commercial_stores',
        value: this.createRentOutOutsideCompoundCommercialStoresConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_out_factory_lands',
        value: this.createRentOutOutsideCompoundFactoryLandsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_out_factories',
        value: this.createRentOutOutsideCompoundFactoriesConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_out_warehouses',
        value: this.createRentOutOutsideCompoundWarehousesConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_out_warehouse_lands',
        value: this.createRentOutOutsideCompoundWarehouseLandsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_out_commercial_administrative_buildings',
        value: this.createRentOutOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_out_vacation_villa',
        value: this.createRentOutOutsideCompoundVacationVillasConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_out_chalets',
        value: this.createRentOutOutsideCompoundChaletsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_out_hotels',
        value: this.createRentOutOutsideCompoundHotelsConfig(stepperModal),
      },
      // RENT-IN OUTSIDE COMPOUND CONFIGURATIONS
      {
        key: 'rentals_outside_compound_rent_in_apartments',
        value: this.createRentInOutsideCompoundApartmentsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_in_duplexes',
        value: this.createRentInOutsideCompoundDuplexesConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_in_studios',
        value: this.createRentInOutsideCompoundStudiosConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_in_penthouses',
        value: this.createRentInOutsideCompoundPenthousesConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_in_basements',
        value: this.createRentInOutsideCompoundBasementsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_in_roofs',
        value: this.createRentInOutsideCompoundRoofsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_in_standalone_villas',
        value: this.createRentInOutsideCompoundStandaloneVillasConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_in_administrative_units',
        value: this.createRentInOutsideCompoundAdministrativeUnitsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_in_medical_clinics',
        value: this.createRentInOutsideCompoundMedicalClinicsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_in_pharmacies',
        value: this.createRentInOutsideCompoundPharmaciesConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_in_commercial_stores',
        value: this.createRentInOutsideCompoundCommercialStoresConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_in_factory_lands',
        value: this.createRentInOutsideCompoundFactoryLandsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_in_factories',
        value: this.createRentInOutsideCompoundFactoriesConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_in_warehouses',
        value: this.createRentInOutsideCompoundWarehousesConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_in_warehouse_lands',
        value: this.createRentInOutsideCompoundWarehouseLandsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_in_commercial_administrative_buildings',
        value: this.createRentInOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_in_vacation_villa',
        value: this.createRentInOutsideCompoundVacationVillasAndChaletsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_in_chalets',
        value: this.createRentInOutsideCompoundVacationVillasAndChaletsConfig(stepperModal),
      },
      {
        key: 'rentals_outside_compound_rent_in_hotels',
        value: this.createRentInOutsideCompoundHotelsConfig(stepperModal),
      },
    ];
  }

  /**
   * Get all available rental outside compound configuration keys
   */
  getRentalOutsideCompoundConfigurationKeys(): string[] {
    return [
      // RENT-OUT OUTSIDE COMPOUND KEYS
      'rentals_outside_compound_rent_out_apartments',
      'rentals_outside_compound_rent_out_duplexes',
      'rentals_outside_compound_rent_out_studios',
      'rentals_outside_compound_rent_out_penthouses',
      'rentals_outside_compound_rent_out_basements',
      'rentals_outside_compound_rent_out_roofs',
      'rentals_outside_compound_rent_out_standalone_villas',
      'rentals_outside_compound_rent_out_administrative_units',
      'rentals_outside_compound_rent_out_medical_clinics',
      'rentals_outside_compound_rent_out_pharmacies',
      'rentals_outside_compound_rent_out_commercial_stores',
      'rentals_outside_compound_rent_out_factory_lands',
      'rentals_outside_compound_rent_out_factories',
      'rentals_outside_compound_rent_out_warehouses',
      'rentals_outside_compound_rent_out_warehouse_lands',
      'rentals_outside_compound_rent_out_commercial_administrative_buildings',
      'rentals_outside_compound_rent_out_vacation_villa',
      'rentals_outside_compound_rent_out_chalets',
      'rentals_outside_compound_rent_out_hotels',
      // RENT-IN OUTSIDE COMPOUND KEYS
      'rentals_outside_compound_rent_in_apartments',
      'rentals_outside_compound_rent_in_duplexes',
      'rentals_outside_compound_rent_in_studios',
      'rentals_outside_compound_rent_in_penthouses',
      'rentals_outside_compound_rent_in_basements',
      'rentals_outside_compound_rent_in_roofs',
      'rentals_outside_compound_rent_in_standalone_villas',
      'rentals_outside_compound_rent_in_administrative_units',
      'rentals_outside_compound_rent_in_medical_clinics',
      'rentals_outside_compound_rent_in_pharmacies',
      'rentals_outside_compound_rent_in_commercial_stores',
      'rentals_outside_compound_rent_in_factory_lands',
      'rentals_outside_compound_rent_in_factories',
      'rentals_outside_compound_rent_in_warehouses',
      'rentals_outside_compound_rent_in_warehouse_lands',
      'rentals_outside_compound_rent_in_commercial_administrative_buildings',
      'rentals_outside_compound_rent_in_vacation_villa',
      'rentals_outside_compound_rent_in_chalets',
      'rentals_outside_compound_rent_in_hotels',
    ];
  }

  /**
   * Check if a key is rent-out outside compound configuration
   */
  isRentOutOutsideCompoundConfiguration(key: string): boolean {
    return key.includes('rentals_outside_compound_rent_out_');
  }

  /**
   * Check if a key is rent-in outside compound configuration
   */
  isRentInOutsideCompoundConfiguration(key: string): boolean {
    return key.includes('rentals_outside_compound_rent_in_');
  }


}
