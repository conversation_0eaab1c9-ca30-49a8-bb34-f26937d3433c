import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BrokersService } from '../../services/brokers.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-broker-details',
  templateUrl: './broker-details.component.html',
  styleUrls: ['./broker-details.component.scss']
})
export class BrokerDetailsComponent implements OnInit {
  broker: any ;

  brokerId: number;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private brokersService: BrokersService,
    private cd: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.brokerId = params['brokerId'];
      if (this.brokerId) {
        this.loadBrokerDetails();
      } else {
        this.router.navigate(['/super-admin/all-brokers']);
      }
    });
  }

  loadBrokerDetails(): void {
    this.brokersService.getBrokerById(this.brokerId).subscribe({
      next: (response) => {
        console.log('Broker details:', response);
        this.broker = response.data  ;
        this.cd.detectChanges();

      },
      error: (error) => {
        console.error('Error loading broker details:', error);
        Swal.fire('Error', 'Failed to load broker details. Please try again.', 'error');
        this.router.navigate(['/super-admin/all-brokers']);
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/super-admin/all-brokers']);
  }

  getInitials(name: any): string {
    if (!name) return '';
    return name.split(' ').map((n:any) => n.charAt(0)).join('').toUpperCase();
  }

  getStatusClass(status: boolean): string {
    return status ? 'badge-light-success' : 'badge-light-danger';
  }

  getStatusText(status: boolean): string {
    return status ? 'Active' : 'Inactive';
  }

  toggleBrokerStatus(): void {
    if (!this.broker) return;

    const action = this.broker.isActive ? 'suspend' : 'activate';
    const message = this.broker.isActive ? 'suspend this broker account?' : 'activate this broker account?';

    Swal.fire({
      title: 'Are you sure?',
      text: `Do you want to ${message}`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: `Yes, ${action}!`
    }).then((result) => {
      if (result.isConfirmed) {
        this.brokersService.toggleBrokerStatus(this.broker.id).subscribe({
          next: (response) => {
            console.log('Status toggled successfully:', response);
            this.broker.isActive = !this.broker.isActive;
            Swal.fire(
              'Success!',
              `Broker account has been ${this.broker.isActive ? 'activated' : 'suspended'}.`,
              'success'
            ).then(() => {
              // Refresh the page after success message
              window.location.reload();
            });
          },
          error: (error) => {
            console.error('Error toggling status:', error);
            Swal.fire('Error', 'Failed to update broker status. Please try again.', 'error');
          }
        });
      }
    });
  }
}
