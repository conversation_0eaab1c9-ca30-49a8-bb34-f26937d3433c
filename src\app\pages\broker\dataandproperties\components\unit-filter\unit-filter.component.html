<div class="filter-dropdown">
   <div class="mb-2">
    <label class="form-label">Area:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.finishingType">
      <option value="">select</option>
      <option *ngFor="let area of areas" [value]="area.id">{{ area.name_en }}</option>
    </select>
  </div>

  <div class="mb-2">
    <label class="form-label">Compound Type:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.compoundType">
      <option value="">Select Compound Type</option>
      <option *ngFor="let type of compoundTypes" [value]="type.value">{{ type.key }}</option>
    </select>
  </div>
  <div class="mb-2">
    <label class="form-label">Unit Area:</label>
    <input
      type="text"
      class="form-control form-control-sm"
      placeholder="Enter unit area"
      [(ngModel)]="filter.unitArea"
    />
  </div>

  <div class="mb-2">
    <label class="form-label">View:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.finishingType">
      <option value="">select</option>
      <option *ngFor="let view of views" [value]="view.value">{{ view.key }}</option>
    </select>
  </div>

  <div class="mb-2">
    <label class="form-label">Unit Type:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.unitType">
      <option value="">Select Unit Type</option>
      <option *ngFor="let type of unitTypes" [value]="type.value">{{ type.key }}</option>
    </select>
  </div>

  <div class="mb-2">
    <label class="form-label">Price:</label>
    <input
      type="number"
      class="form-control form-control-sm"
      placeholder="Enter price"
      [(ngModel)]="filter.price"
    />
  </div>

  <!-- <div class="mb-2">
    <label class="form-label">Finishing Status:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.finishingType">
      <option value="">Select Finishing</option>
      <option *ngFor="let type of finishingTypes" [value]="type.value">{{ type.key }}</option>
    </select>
  </div> -->

  <!-- <div class="mb-2">
    <label class="form-label">Status:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.status">
      <option value="">Select Status</option>
      <option *ngFor="let state of status" [value]="state.value">{{ state.key }}</option>
    </select>
  </div> -->



  <div class="d-flex gap-2">
    <button class="btn btn-sm btn-primary flex-fill" (click)="apply()">Apply</button>
    <button class="btn btn-sm btn-secondary flex-fill" (click)="reset()">Reset</button>
  </div>
</div>
