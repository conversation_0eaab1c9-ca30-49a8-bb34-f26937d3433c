<div class="mb-5 mt-0">
  <app-broker-title></app-broker-title>
</div>

<div class="card mb-5 mb-xl-5">
  <div class="card-body pt-3 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap mb-1">
      <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start flex-wrap mb-2">
          <div class="d-flex my-4">
            <h1 class="text-dark-blue fs-2 fw-bolder me-1">Requests</h1>
          </div>

          <div class="d-flex my-4 mt-0">
            <form data-kt-search-element="form" class="w-300px position-relative mb-3" autocomplete="off">
              <app-keenicon name="magnifier"
                class="fs-2 fs-lg-1 text-gray-500 position-absolute top-50 translate-middle-y ms-3"></app-keenicon>
              <input type="text" class="form-control form-control-flush ps-10 bg-light border rounded-pill"
                name="search" value="" placeholder="Search..." data-kt-search-element="input" />
            </form>
          </div>

          <div class="d-flex my-4">
            <a class="btn btn-sm btn-light-dark-blue me-3 cursor-pointer">
              <i class="fa-solid fa-filter"></i> Filter
            </a>
            <a class="btn btn-sm btn-light-dark-blue me-3 cursor-pointer">
              <i class="fa-solid fa-arrow-down-wide-short"></i> Sort
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="table-responsive">
      <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
        <thead>
          <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
            <th class="w-25px ps-4 rounded-start">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="1" data-kt-check="true"
                  data-kt-check-target=".widget-13-check" [(ngModel)]="selectAll" (change)="selectAllBrokers()" />
              </div>
            </th>
            <th class="min-w-150px">
              Broker name
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-150px">
              Account type
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-100px">Type</th>
            <th class="min-w-100px">
              Area <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-150px">
              Specialization
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-100px text-end rounded-end pe-4">Actions</th>
          </tr>
        </thead>

        <tbody>
          <tr *ngFor="let broker of AssignBroker; trackBy: trackById">
            <td class="ps-4">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input widget-13-check" type="checkbox" value="1" [(ngModel)]="broker.selected"
                  (change)="updateSelectAllStatus()" />
              </div>
            </td>

            <td>
              <div class="d-flex align-items-center">
                <div class="symbol symbol-45px me-5">
                  <img [src]="broker.image" alt="" class="rounded-circle" />
                </div>
                <div class="d-flex justify-content-start flex-column">
                  <a class="text-gray-900 fw-bold text-hover-dark-blue fs-5">
                    {{ broker.fullName }}
                  </a>
                </div>
              </div>
            </td>

            <td>
              <span *ngIf="broker.accountType === 'Golden Account'" class="badge badge-light-warning fs-5">
                {{ broker.accountType }}
              </span>
              <span *ngIf="broker.accountType === 'Silver Account'" class="badge badge-light-primary fs-5">
                {{ broker.accountType }}
              </span>
              <span *ngIf="broker.accountType === 'Bronze Account'" class="badge badge-light-danger fs-5">
                {{ broker.accountType }}
              </span>
              <span *ngIf="broker.accountType === 'Free'" class="badge text-dark bg-light fs-5">
                {{ broker.accountType }}
              </span>
            </td>

            <td>
              <span *ngIf="broker.type === 'Real Estate Brokage Company'" class="badge badge-light-info fs-5">
                {{ broker.type }}
              </span>
              <span *ngIf="broker.type === 'Independent'" class="badge badge-light-success fs-5">
                {{ broker.type }}
              </span>
            </td>

            <td>
              <div *ngFor="let area of broker.areas;">
                <span class="badge badge-dark-blue fs-7 m-1">{{ area.nameEn }} | {{ area.nameAr }}</span>
              </div>
            </td>

            <td>
              <div *ngFor="let specialization of broker.specializations;">
                <span class="badge badge-dark-blue fs-7 m-1">{{ specialization.specialization }}</span>
              </div>
            </td>

            <td class="text-end pe-4">
              <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
                data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                <i class="fa-solid fa-ellipsis-vertical"></i>
              </button>
              <app-assignto-brokers-action-menu></app-assignto-brokers-action-menu>
            </td>
          </tr>

          <tr>
            <td colspan="7" class="text-center">
              <!-- <button class="btn btn-light-dark-blue mt-4 fw-bolder">
                Assign to broker ({{ selectedCount }})
              </button> -->
              <button class="btn btn-light-dark-blue mt-4 fw-bolder" (click)="assignSelectedBrokers()"
                [disabled]="selectedCount === 0" routerLink="/requests/sent">
                Assign to broker ({{ selectedCount }})
              </button>

            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>