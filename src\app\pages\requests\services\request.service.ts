import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { AbstractCrudService } from '../../shared/services/abstract-crud.service';
import { environment } from 'src/environments/environment';

export interface Filters {
  status?: string;
  page?: number;
  [key: string]: string | number | boolean | undefined;
}

@Injectable({
  providedIn: 'root'
})

export class RequestService extends AbstractCrudService {

  apiUrl = `${environment.apiUrl}/requests`;
  request: any;
  private requestId: string | null = null;
  private filtersSubject = new BehaviorSubject<Filters>({});
  private requestSubject = new BehaviorSubject<any | null>(null);
  private newRequestsCountSubject = new BehaviorSubject<number>(0); // Add BehaviorSubject for newRequestsCount

  constructor(http: HttpClient) {
    super(http);
  }

  // Clear previous request data
  clearRequest() {
    this.requestSubject.next(null);
  }

  // Set the request data
  setRequest(request: any) {
    this.requestSubject.next(request);
  }

  // Get the request data as an observable
  getRequest(): Observable<any | null> {
    return this.requestSubject.asObservable();
  }

  // Get the current request value (synchronous)
  getCurrentRequest(): any | null {
    return this.requestSubject.getValue();
  }

  // Set the request ID
  setRequestId(id: string) {
    this.requestId = id;
  }

  // Get the request ID
  getRequestId(): string | null {
    return this.requestId;
  }

  // Fetch request by ID from the backend
  getRequestById(id: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/${id}`);
  }

  setFilters(filters: Filters) {
    this.filtersSubject.next(filters);
  }

  getFilters(): Observable<Filters> {
    return this.filtersSubject.asObservable();
  }

  // newRequestsCount
  setNewRequestsCount(count: number) {
    this.newRequestsCountSubject.next(count);
  }

  getNewRequestsCount(): Observable<number> {
    return this.newRequestsCountSubject.asObservable();
  }

  getRecommendedUnits(requestId: any, brokerId: any, page: any): Observable<any> {
    let offset = page.size * page.pageNumber;
    let queryParams = {
      limit: page.size,
      offset: offset,
      sort: 'desc',
      sortBy: 'id',
    };
    let params = this.appendPageFilter(page, queryParams);
    return this.http.get<any[]>(`${this.apiUrl}/recommend/units/${requestId}/${brokerId}`, { params });
  }

  makeReply(requestId: any, brokerId: any, selectedUnitsIds: any): Observable<any> {
    const body = {
      brokerId,
      requestId,
      unitIds: selectedUnitsIds
    };
    return this.http.post(`${environment.apiUrl}/request/replies`, body);
  }

  getReplies(requestId: any, page: any): Observable<any> {
    let offset = page.size * page.pageNumber;
    let queryParams = {
      limit: page.size,
      offset: offset,
      sort: 'desc',
      sortBy: 'id',
      requestId: requestId,
    };
    let params = this.appendPageFilter(page, queryParams);
    return this.http.get<any[]>(`${environment.apiUrl}/request/replies`, { params });
  }

  getRequestHistory(id: number) {
    return this.http.get(`${this.apiUrl}/history/${id}`);
  }

  getRecommendedBrokers(id: number) {
    return this.http.get(`${this.apiUrl}/recommend/brokers/${id}`);
  }

  assignBrokersToRequest(requestId: number, brokerIds: number[]) {
    return this.http.post(`${this.apiUrl}/assign-to-brokers/${requestId}`, {
      brokerIds: brokerIds
    });
  }

  updateRequestStatus(requestId: number, payload: { userId: number; status: string }): Observable<any> {
    return this.http.post(`${this.apiUrl}/update-status/${requestId}`, payload);
  }

  archiveRequest(requestId: number, brokerId: number): Observable<any> {
    return this.http.post(`${this.apiUrl}/archive/${requestId}/${brokerId}`, null);
  }
}
