import { Component, HostBinding, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-account-management-card',
  templateUrl: './account-management-card.component.html',
  styleUrls: ['./account-management-card.component.scss']
})
export class AccountManagementCardComponent {

  @Input() faIcon: string = '';
  @Input() title: string = '';
  @Input() description: string = '';
  @Input() routePath: string = '';
  @Input() count: string = '0';
  @Input() countLabel: string = 'Items';
  @Output() cardClick = new EventEmitter<string>();
  @HostBinding('class') class = 'card h-100 cursor-pointer account-management-card';

  constructor() {}

  get isFontAwesome(): boolean {
    return !!this.faIcon;
  }

  onCardClick(): void {
    if (this.routePath) {
      this.cardClick.emit(this.routePath);
    }
  }
}
