<div
  class="card-body d-flex flex-column justify-content-between mt-9 bgi-no-repeat bgi-size-cover bgi-position-x-center pb-0">
  <img class="mx-auto h-150px h-lg-200px theme-light-show"
    src="../../../../assets/media/broker/empty-data-and-properties.png" alt="" />
  <img class="mx-auto h-150px h-lg-200px theme-dark-show"
    src="../../../../assets/media/broker/empty-data-and-properties.png" alt="" />
  <div class="mb-10 mt-10">
    <div class="fs-2hx fw-bold text-dark-blue text-center mb-1">
      <span class="me-2">There is nothing currently!</span>
    </div>

    <div class="fs-4 fw-bold text-gray-500 text-center mb-5">
      <span>{{ displayMessage }}</span>
    </div>

    <!-- Broker-only buttons -->
    <div *ngIf="userRole === 'broker' " class="text-center mb-2">
      <input type="file" #fileInput (change)="onFileSelected($event)" accept=".xlsx,.xls" hidden />
      <button type="button" class="btn btn-md btn-dark-blue fw-bold" (click)="fileInput.click()">
        <img class="mx-auto h-30px" src="../../../../assets/media/broker/excellogo.png" alt="" />
        Upload Property Units File
      </button>
    </div>

    <div *ngIf="userRole === 'broker'" class="text-center">
      <button type="button" class="fw-bold text-success bg-transparent border-0 cursor-pointer"
        (click)="downloadTemplate()">
        Download Ads-template.xls
        <app-keenicon name="folder-down" class="text-success fs-3" type="outline"></app-keenicon>
      </button>
    </div>
  </div>
</div>
