<div class="mb-5 mt-0">
  <app-broker-title></app-broker-title>
</div>

<div class="card mb-5 mb-xl-5">
  <div class="card-body pt-3 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap mb-3">
      <div class="flex-grow-1">
        <div
          class="d-flex justify-content-between align-items-start flex-wrap mb-2"
        >
          <div class="d-flex my-4">
            <h1 class="text-dark-blue fs-2 fw-bolder me-1">Requests</h1>
          </div>

          <div class="d-flex my-4">
            <form
              data-kt-search-element="form"
              class="w-300px position-relative mb-3"
              autocomplete="off"
            >
              <app-keenicon
                name="magnifier"
                class="fs-2 fs-lg-1 text-gray-500 position-absolute top-50 translate-middle-y ms-3"
              ></app-keenicon>
              <input
                type="text"
                class="form-control form-control-flush ps-10 bg-light border rounded-pill"
                name="search"
                value=""
                placeholder="Search..."
                data-kt-search-element="input"
              />
            </form>
          </div>

          <div class="d-flex my-4">
            <a class="btn btn-sm btn-light-dark-blue me-3 cursor-pointer">
              <i class="fa-solid fa-filter"></i> Filter
            </a>
            <a class="btn btn-sm btn-light-dark-blue me-3 cursor-pointer">
              <i class="fa-solid fa-arrow-down-wide-short"></i> Sort
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="table-responsive">
      <table
        class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5 text-nowrap"
      >
        <thead>
          <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
            <th class="w-25px ps-4 rounded-start">
              <div
                class="form-check form-check-sm form-check-custom form-check-solid"
              >
                <input
                  class="form-check-input"
                  type="checkbox"
                  value="1"
                  data-kt-check="true"
                  data-kt-check-target=".widget-13-check"
                  [(ngModel)]="selectAll"
                  (change)="selectAllRequests()"
                />
              </div>
            </th>

            <th class="min-w-150px">
              Request
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>

            <th class="min-w-140px">
              Order Date
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>

            <th class="min-w-100px">
              Responses
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>

            <th class="min-w-130px">
              Specialization
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>

            <th class="min-w-130px">
              type
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>

            <th class="min-w-130px">
              Area
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>

            <th class="min-w-130px">
              Status
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>

            <th class="min-w-130px text-end rounded-end pe-4">Actions</th>
          </tr>
        </thead>

        <tbody>
          <tr *ngFor="let request of AssignRequest; trackBy: trackById">
            <td class="ps-4">
              <div
                class="form-check form-check-sm form-check-custom form-check-solid"
              >
                <input
                  class="form-check-input widget-13-check"
                  type="checkbox"
                  [(ngModel)]="request.selected"
                  (change)="updateSelectAllStatus()"
                />
              </div>
            </td>

            <td class="text-gray-900 fw-bold fs-6">
              {{ request.request }}
            </td>

            <td class="text-gray-900 fw-bold fs-6">
              {{ request.orderDate }}
            </td>

            <td class="text-gray-900 fw-bold fs-6 text-center">
              {{ request.responses }}
            </td>

            <td>
              <span class="badge badge-dark-blue">
                {{ request.specialization }}
              </span>
            </td>

            <td>
              <span
                *ngIf="request.type === 'Compound'"
                class="badge badge-light-warning"
              >
                {{ request.type }}
              </span>
              <span
                *ngIf="request.type === 'Outside compound'"
                class="badge badge-primary"
              >
                {{ request.type }}
              </span>
              <span
                *ngIf="request.type === 'Administrative building'"
                class="badge"
                style="background-color: #edabff"
              >
                {{ request.type }}
              </span>
              <span
                *ngIf="request.type === 'Free account'"
                class="badge badge-success"
              >
                {{ request.type }}
              </span>
            </td>

            <td class="text-gray-900 fw-bold fs-6">
              {{ request.area }}
            </td>

            <td>
              <span
                *ngIf="request.status === 'In progress'"
                class="badge badge-light-warning"
              >
                {{ request.status }}
              </span>
              <span
                *ngIf="request.status === 'The request has been completed'"
                class="badge badge-light-success"
              >
                {{ request.status }}
              </span>
            </td>

            <td class="text-end pe-4">
              <div class="position-relative">
                <button
                  type="button"
                  class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
                  data-kt-menu-trigger="click"
                  data-kt-menu-placement="bottom-end"
                  data-kt-menu-flip="top-end"
                >
                  <i class="fa-solid fa-ellipsis-vertical"></i>
                </button>

                <div
                  class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold w-200px"
                  data-kt-menu="true"
                >
                  <div class="menu-item px-3">
                    <div
                      class="menu-content fs-6 fw-bolder px-3 py-4 text-dark-blue text-start"
                    >
                      Actions
                    </div>
                  </div>

                  <div class="separator mb-3 opacity-75"></div>

                  <div class="menu-item px-3">
                    <a
                      class="menu-link px-3 cursor-pointer text-hover-dark-blue"
                      [routerLink]="['/requests/render', request.id]"
                    >
                      <i class="fa-solid fa-eye me-1"></i>
                      View
                    </a>
                  </div>
                  <div class="menu-item px-3">
                    <a
                      class="menu-link px-3 cursor-pointer text-hover-dark-blue"
                      [routerLink]="['/requests/assign-to', request.id]"
                    >
                      <i class="fa-solid fa-share me-1"></i>
                      Assign to broker(s)
                    </a>
                  </div>

                  <div class="separator mt-3 opacity-75"></div>

                  <div class="menu-item px-3">
                    <div class="menu-content px-3 py-3">
                      <a class="btn btn-danger btn-sm px-4 cursor-pointer">
                        <i class="fa-regular fa-eye-slash"></i>
                        Archive
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- <app-pagination
        [totalItems]="page.totalElements"
        [itemsPerPage]="page.limit"
        [currentPage]="page.pageNumber"
        (pageChange)="onPageChange($event)">
      </app-pagination> -->
    </div>
  </div>
</div>
