.symbol {
  &.symbol-circle {
    .symbol-label {
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100px;
      height: 100px;
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;

      .icon-3x {
        font-size: 3rem;
        transition: all 0.3s ease;
      }

      &:hover .svg-icon svg {
        transform: scale(1.1);
        path, circle, rect {
          stroke-width: 2;
        }
      }

      &:hover .icon-3x {
        transform: scale(1.1);
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }
    }
  }
}

.card-title {
  color: #2d3748;
  transition: color 0.3s ease;
  margin-bottom: 1rem;
}

.statistics-section {
  background: linear-gradient(135deg, #d3e9fe 0%, #f0f6fc 100%);
  border-radius: 12px;
  padding: 1rem 1.5rem;
  margin: 1rem 0;
}
