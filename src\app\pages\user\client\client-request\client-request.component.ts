import { Component } from '@angular/core';

@Component({
  selector: 'app-client-request',
  // standalone: true,
  // imports: [],
  templateUrl: './client-request.component.html',
  styleUrl: './client-request.component.scss'
})
export class ClientRequestComponent {

  AssignRequest = [
    {
      id: 1,
      request: 'Request 1',
      orderDate: '2025-04-28',
      responses: 5,
      specialization: 'Resident',
      type: 'Outside compound',
      area: 'Fifth Settlement',
      status: 'In progress',
      selected: false,
    },
    {
      id: 2,
      request: 'Request 2',
      orderDate: '2025-04-25',
      responses: 3,
      specialization: 'Admin',
      type: 'Compound',
      area: 'Nasr City',
      status: 'The request has been completed',
      selected: false,
    },
    {
      id: 3,
      request: 'Request 3',
      orderDate: '2025-04-20',
      responses: 8,
      specialization: 'Admin',
      type: 'Administrative building',
      area: 'Fifth Settlement',
      status: 'In progress',
      selected: false,
    },
    {
      id: 4,
      request: 'Request 4',
      orderDate: '2025-04-18',
      responses: 2,
      specialization: 'Resident',
      type: 'Compound',
      area: 'Nasr City',
      status: 'The request has been completed',
      selected: false,
    },
  ];


  trackById(index: number, request: any): number {
    return request.id;
  }

  selectAll = false;

  selectAllRequests() {
    this.AssignRequest.forEach((request) => {
      request.selected = this.selectAll;
    });
    this.printSelectedIds();
  }

  updateSelectAllStatus() {
    this.selectAll = this.AssignRequest.every((request) => request.selected);
    this.printSelectedIds();
  }

  printSelectedIds() {
    const selectedIds = this.AssignRequest.filter(
      (request) => request.selected
    ).map((request) => request.id);

    console.log('Selected IDs:', selectedIds);
  }

}
