import { Component, Input, AfterViewInit } from '@angular/core';
import { MenuComponent } from 'src/app/_metronic/kt/components';

@Component({
  selector: 'app-sent-requests-actions-menu',
  templateUrl: './sent-requests-actions-menu.component.html',
  styleUrls: ['./sent-requests-actions-menu.component.scss']
})
export class SentRequestsActionsMenuComponent implements AfterViewInit {
  @Input() requestId: number;

  ngAfterViewInit() {
    // Initialize menu after view is ready
    setTimeout(() => {
      MenuComponent.reinitialization();
    }, 100);
  }
}
