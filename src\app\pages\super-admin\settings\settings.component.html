<!-- Settings Header using Metronic classes -->
<div class="row mb-8">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title fs-2 fw-bold text-gray-900">System Settings & Administration</h3>
        <div class="card-toolbar">
          <button class="btn btn-primary btn-sm">
            <i class="fas fa-cog me-2"></i>
            Advanced Settings
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Admin Feature Cards using Metronic spacing -->
<div class="row justify-content-center g-6 g-xl-9">
  <div class="col-lg-4 col-md-6" *ngFor="let feature of adminFeatures">
    <app-admin-card [faIcon]="feature.faIcon" [title]="feature.title" [description]="feature.description"
      [backgroundColor]="feature.backgroundColor" [iconColor]="feature.iconColor" [routePath]="feature.routePath"
      (cardClick)="onCardClick($event)">
    </app-admin-card>
  </div>
</div>