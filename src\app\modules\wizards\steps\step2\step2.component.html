<div class="pb-10 pb-lg-15">
  <h2 class="fw-bolder text-gray-900">Account Info</h2>
  <div class="text-gray-500 fw-bold fs-6">
    If you need more info, please check out
    <a href="#" class="link-primary fw-bolder"> Help Page</a>.
  </div>
</div>
<div [formGroup]="form">
  <div class="mb-10 fv-row">
    <label class="d-flex align-items-center form-label mb-3">
      Specify Team Size<i
      class="fas fa-exclamation-circle ms-2 fs-7"
      ngbTooltip="Provide your team size to help us setup your billing">

    </i>
    </label>
    <div class="row mb-2" data-kt-buttons="true">
      <div class="col">
        <input
          formControlName="accountTeamSize"
          type="radio"
          class="btn-check"
          id="kt_account_team_size_select_1"
          value="1-1"/>
        <label
          class="
            btn btn-outline btn-outline-dashed btn-outline-default
            w-100
            p-4
          "
          for="kt_account_team_size_select_1">
          <span class="fw-bolder fs-3">1-1</span>
        </label>

      </div>
      <div class="col">
        <input
          formControlName="accountTeamSize"
          type="radio"
          class="btn-check"
          id="kt_account_team_size_select_2"
          value="2-10"/>
        <label
          class="
            btn btn-outline btn-outline-dashed btn-outline-default
            w-100
            p-4
          "
          for="kt_account_team_size_select_2">
          <span class="fw-bolder fs-3">2-10</span>
        </label>

      </div>
      <div class="col">
        <input
          formControlName="accountTeamSize"
          type="radio"
          class="btn-check"
          id="kt_account_team_size_select_3"
          value="10-50"/>
        <label
          class="
            btn btn-outline btn-outline-dashed btn-outline-default
            w-100
            p-4
          "
          for="kt_account_team_size_select_3">
          <span class="fw-bolder fs-3">10-50</span>
        </label>

      </div>
      <div class="col">
        <input
          formControlName="accountTeamSize"
          type="radio"
          class="btn-check"
          id="kt_account_team_size_select_4"
          value="50+"/>
        <label
          class="
            btn btn-outline btn-outline-dashed btn-outline-default
            w-100
            p-4
          "
          for="kt_account_team_size_select_4">
          <span class="fw-bolder fs-3">50+</span>
        </label>

      </div>
    </div>
    <div class="form-text">
      Customers will see this shortened version of your statement descriptor
    </div>
  </div>
  <div class="mb-10 fv-row">
    <label class="form-label mb-3 required">Team Account Name</label>
    <input
      formControlName="accountName"
      type="text"
      class="form-control form-control-lg form-control-solid"
      placeholder="Team Account name"
    />
    <div
      *ngIf="
        form.get('accountName')?.hasError('required') &&
        form.get('accountName')?.touched
      "
      class="fv-plugins-message-container invalid-feedback">

      Team Account name is required
    </div>
  </div>
  <div class="mb-0 fv-row">
    <label class="d-flex align-items-center form-label mb-5">
      Select Account Plan<i
      class="fas fa-exclamation-circle ms-2 fs-7"
      ngbTooltip="Monthly billing will be based on your account plan">

    </i>
    </label>
    <div class="mb-0">
      <label class="d-flex flex-stack mb-5 cursor-pointer">
        <span class="d-flex align-items-center me-2">
          <span class="symbol symbol-50px me-6">
            <span class="symbol-label">
              <app-keenicon name="bank" class="fs-1"></app-keenicon>
            </span>
          </span>
          <span class="d-flex flex-column">
            <span class="fw-bolder text-gray-800 text-hover-primary fs-5">
              Company Account
            </span>
            <span class="fs-6 fw-bold text-gray-500">
              Use images to enhance your post flow
            </span>
          </span>
        </span>
        <span class="form-check form-check-custom form-check-solid">
          <input
            formControlName="accountPlan"
            class="form-check-input"
            type="radio"
            value="1"/></span>
      </label>
      <label class="d-flex flex-stack mb-5 cursor-pointer">
        <span class="d-flex align-items-center me-2">
          <span class="symbol symbol-50px me-6">
            <span class="symbol-label">
              <app-keenicon name="chart" class="fs-1"></app-keenicon>
            </span>
          </span>
          <span class="d-flex flex-column">
            <span class="fw-bolder text-gray-800 text-hover-primary fs-5">
              Developer Account
            </span>
            <span class="fs-6 fw-bold text-gray-500">
              Use images to your post time
            </span>
          </span>
        </span>
        <span class="form-check form-check-custom form-check-solid">
          <input
            formControlName="accountPlan"
            class="form-check-input"
            type="radio"
            value="2"/></span>
      </label>
      <label class="d-flex flex-stack mb-0 cursor-pointer">
        <span class="d-flex align-items-center me-2">
          <span class="symbol symbol-50px me-6">
            <span class="symbol-label">
              <app-keenicon name="chart-pie-4" class="fs-1"></app-keenicon>
            </span>
          </span>
          <span class="d-flex flex-column">
            <span class="fw-bolder text-gray-800 text-hover-primary fs-5">
              Testing Account
            </span>
            <span class="fs-6 fw-bold text-gray-500">
              Use images to enhance time travel rivers
            </span>
          </span>
        </span>
        <span class="form-check form-check-custom form-check-solid">
          <input
            formControlName="accountPlan"
            class="form-check-input"
            type="radio"
            value="3"/></span>
      </label>
    </div>
  </div>
</div>
