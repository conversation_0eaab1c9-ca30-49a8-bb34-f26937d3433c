<ng-container *ngIf="title$ | async as title">
  <!-- <div class="card mb-1 mb-xl-1">
    <div class="card-body pt-9 pb-0">
      <div class="d-flex flex-wrap flex-sm-nowrap mb-3">
        <div class="me-7 mb-4">
          <div class="symbol symbol-65px symbol-65px symbol-fixed position-relative">
            <img src="./assets/media/avatars/300-1.jpg" alt="User" />
          </div>
        </div>
        <div class="flex-grow-1">
          <div class="d-flex justify-content-between align-items-start flex-wrap mb-2">
            <div class="d-flex flex-column">
              <div class="d-flex align-items-center mb-2">
                <span class="text-gray-800 fs-2 fw-bolder me-1">
                  Hello -
                </span>
                <a class="text-gray-800 text-hover-primary fs-2 fw-semibold me-1 cursor-pointer">
                    <PERSON><PERSON>
                </a>
                <span class="btn btn-sm btn-light-warning fw-bolder ms-2 fs-8 py-1 px-3">
                  Golden Account
                </span>
              </div>

              <div class="d-flex flex-wrap fw-bold fs-6 mb-4 pe-2">
                <span class="d-flex align-items-center me-5 mb-2 fw-bolder py-2 px-3 badge badge-dark-blue text-white">
                  Residential
                </span>
                <span class="d-flex align-items-center me-5 mb-2 fw-bolder py-2 px-3 badge badge-dark-blue text-white">
                  Administrative
                </span>
                <span class="d-flex align-items-center me-5 mb-2 fw-bolder py-2 px-3 badge badge-dark-blue text-white">
                  Commercial
                </span>
                <span class="d-flex align-items-center me-5 mb-2 fw-bolder py-2 px-3 badge badge-dark-blue text-white">
                  Hotel
                </span>
                <span class="d-flex align-items-center me-5 mb-2 fw-bolder py-2 px-3 badge badge-light-dark-blue text-dark-blue">
                  Fifth Settlement
                </span>
                <span class="d-flex align-items-center me-5 mb-2 fw-bolder py-2 px-3 badge badge-light-dark-blue text-dark-blue">
                  Nasr City
                </span>
                <span class="d-flex align-items-center me-5 mb-2 fw-bolder py-2 px-3 badge badge-light-dark-blue text-dark-blue">
                  New Cairo
                </span>
                <span class="d-flex align-items-center me-5 mb-2 fw-bolder py-2 px-3 badge badge-light-dark-blue text-dark-blue">
                  Al Rehab
                </span>
              </div>
            </div>
            <div class="d-flex my-4">
              <a class="btn btn-sm btn-dark-blue me-3 cursor-pointer btn-active-light-dark-blue">
                <i class="fa-solid fa-plus"></i>
                Create New Request
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div> -->
</ng-container>
