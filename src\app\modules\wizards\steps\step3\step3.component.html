<div class="pb-10 pb-lg-12">
  <h2 class="fw-bolder text-gray-900">Business Details</h2>
  <div class="text-gray-500 fw-bold fs-6">
    If you need more info, please check out
    <a href="#" class="link-primary fw-bolder">Help Page</a>.
  </div>
</div>
<div [formGroup]="form">
  <div class="fv-row mb-10">
    <label class="form-label required">Business Name</label
    ><input
      name="businessName"
      class="form-control form-control-lg form-control-solid"
      formControlName="businessName"
    />
    <div
      class="fv-plugins-message-container invalid-feedback"
      *ngIf="
        form.get('businessName')?.hasError('required') &&
        form.get('businessName')?.touched
      "
    >
      Business Name is required
    </div>
  </div>
  <div class="fv-row mb-10">
    <label class="d-flex align-items-center form-label"
      ><span class="required">Shortened Descriptor</span></label
    ><input
      name="businessDescriptor"
      class="form-control form-control-lg form-control-solid"
      formControlName="businessDescriptor"
    />
    <div
      class="fv-plugins-message-container invalid-feedback"
      *ngIf="
        form.get('businessDescriptor')?.hasError('required') &&
        form.get('businessDescriptor')?.touched
      "
    >
      Shortened Descriptor is required
    </div>
    <div class="form-text">
      Customers will see this shortened version of your statement descriptor
    </div>
  </div>
  <div class="fv-row mb-10">
    <label class="form-label required">Corporation Type</label
    ><select
      name="businessType"
      class="form-select form-select-lg form-select-solid"
      formControlName="businessType"
    >
      <option value="1">S Corporation</option>
      <option value="1">C Corporation</option>
      <option value="2">Sole Proprietorship</option>
      <option value="3">Non-profit</option>
      <option value="4">Limited Liability</option>
      <option value="5">General Partnership</option>
    </select>
  </div>
  <div class="fv-row mb-10">
    <label class="form-label">Business Description</label
    ><textarea
      name="businessDescription"
      class="form-control form-control-lg form-control-solid"
      rows="3"
      formControlName="businessDescription"
    ></textarea>
  </div>
  <div class="fv-row mb-0">
    <label class="fs-6 fw-bold form-label required">Contact Email</label
    ><input
      name="businessEmail"
      class="form-control form-control-lg form-control-solid"
      formControlName="businessEmail"
    />
    <div
      class="fv-plugins-message-container invalid-feedback"
      *ngIf="
        form.get('businessEmail')?.hasError('required') &&
        form.get('businessEmail')?.touched
      "
    >
      Contact Email is required
    </div>
    <div
      class="fv-plugins-message-container invalid-feedback"
      *ngIf="
        form.get('businessEmail')?.hasError('email') &&
        form.get('businessEmail')?.touched
      "
    >
      Wrong email format
    </div>
  </div>
</div>
