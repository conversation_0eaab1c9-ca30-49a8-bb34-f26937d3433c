<div class="card mb-5 mb-xl-10">
  <div
    class="card-header border-0 cursor-pointer"
    role="button"
    data-bs-toggle="collapse"
    data-bs-target="#kt_account_connected_accounts"
    aria-expanded="true"
    aria-controls="kt_account_connected_accounts"
  >
    <div class="card-title m-0">
      <h3 class="fw-bolder m-0">Connected Accounts</h3>
    </div>
  </div>

  <div id="kt_account_connected_accounts" class="collapse show">
    <div class="card-body border-top p-9">
      <div
        class="
          notice
          d-flex
          bg-light-primary
          rounded
          border-primary border border-dashed
          mb-9
          p-6
        "
      >
        <app-keenicon name="design-frame" class="fs-2 text-primary me-4"></app-keenicon>
        <div class="d-flex flex-stack flex-grow-1">
          <div class="fw-bold">
            <div class="fs-6 text-gray-600">
              Two-factor authentication adds an extra layer of security to your
              account. To log in, in you'll need to provide a 4 digit amazing
              code.
              <a href="#" class="fw-bolder"> Learn More </a>
            </div>
          </div>
        </div>
      </div>

      <div class="py-2">
        <div class="d-flex flex-stack">
          <div class="d-flex">
            <img
              src="./assets/media/svg/brand-logos/google-icon.svg"
              class="w-30px me-6"
              alt=""
            />

            <div class="d-flex flex-column">
              <a href="#" class="fs-5 text-gray-900 text-hover-primary fw-bolder">
                Google
              </a>
              <div class="fs-6 fw-bold text-gray-500">
                Plan properly your workflow
              </div>
            </div>
          </div>
          <div class="d-flex justify-content-end">
            <div class="form-check form-check-solid form-switch">
              <input
                class="form-check-input w-45px h-30px"
                type="checkbox"
                id="googleswitch"
                checked
              />
              <label class="form-check-label" htmlFor="googleswitch"></label>
            </div>
          </div>
        </div>

        <div class="separator separator-dashed my-5"></div>

        <div class="d-flex flex-stack">
          <div class="d-flex">
            <img
              src="./assets/media/svg/brand-logos/github.svg"
              class="w-30px me-6"
              alt=""
            />

            <div class="d-flex flex-column">
              <a href="#" class="fs-5 text-gray-900 text-hover-primary fw-bolder">
                Github
              </a>
              <div class="fs-6 fw-bold text-gray-500">
                Keep eye on on your Repositories
              </div>
            </div>
          </div>
          <div class="d-flex justify-content-end">
            <div class="form-check form-check-solid form-switch">
              <input
                class="form-check-input w-45px h-30px"
                type="checkbox"
                id="githubswitch"
              />
              <label class="form-check-label" htmlFor="githubswitch"></label>
            </div>
          </div>
        </div>

        <div class="separator separator-dashed my-5"></div>

        <div class="d-flex flex-stack">
          <div class="d-flex">
            <img
              src="./assets/media/svg/brand-logos/slack-icon.svg"
              class="w-30px me-6"
              alt=""
            />

            <div class="d-flex flex-column">
              <a href="#" class="fs-5 text-gray-900 text-hover-primary fw-bolder">
                Slack
              </a>
              <div class="fs-6 fw-bold text-gray-500">
                Integrate Projects Discussions
              </div>
            </div>
          </div>
          <div class="d-flex justify-content-end">
            <div class="form-check form-check-solid form-switch">
              <input
                class="form-check-input w-45px h-30px"
                type="checkbox"
                checked
              />
              <label class="form-check-label" htmlFor="slackswitch"></label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="card-footer d-flex justify-content-end py-6 px-9">
      <button type="button" class="btn btn-light btn-active-light-primary me-2">
        Discard
      </button>
      <button type="button" (click)="saveSettings()" class="btn btn-primary">
        <ng-container *ngIf="!isLoading">Save Changes</ng-container>
        <ng-container *ngIf="isLoading">
          <span class="indicator-progress" [style.display]="'block'">
            Please wait...{{ " " }}
            <span
              class="spinner-border spinner-border-sm align-middle ms-2"
            ></span>
          </span>
        </ng-container>
      </button>
    </div>
  </div>
</div>
