<div class="container-fluid">
  <!-- Page Header -->
  <div class="row mb-5">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">
            {{ isEditMode ? 'Edit Subscription' : 'Create New Subscription' }}
          </h3>
          <div class="card-toolbar">
            <button class="btn btn-secondary btn-sm me-2" (click)="onCancel()">
              <i class="fas fa-arrow-left me-2"></i>
              Back to Subscriptions
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Subscription Form -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <form [formGroup]="subscriptionForm" (ngSubmit)="onSubmit()">
            <div class="row">
              <!-- Basic Information -->
              <div class="col-md-6">
                <h5 class="mb-4">Basic Information</h5>

                <!-- Name -->
                <div class="mb-4">
                  <label class="form-label required">Subscription Name</label>
                  <input
                    type="text"
                    class="form-control"
                    formControlName="name"
                    [class.is-invalid]="isFieldInvalid('name')"
                    placeholder="Enter subscription name">
                  <div class="invalid-feedback" *ngIf="isFieldInvalid('name')">
                    {{ getFieldError('name') }}
                  </div>
                </div>

                <!-- Description -->
                <div class="mb-4">
                  <label class="form-label">Description</label>
                  <textarea
                    class="form-control"
                    formControlName="description"
                    rows="3"
                    placeholder="Enter subscription description"></textarea>
                </div>

                <!-- Price -->
                <div class="mb-4">
                  <label class="form-label required">Price</label>
                  <input
                    type="number"
                    class="form-control"
                    formControlName="price"
                    [class.is-invalid]="isFieldInvalid('price')"
                    placeholder="0"
                    min="0">
                  <div class="invalid-feedback" *ngIf="isFieldInvalid('price')">
                    {{ getFieldError('price') }}
                  </div>
                </div>

                <!-- Image Upload -->
                <div class="mb-4">
                  <label class="form-label" [class.required]="!isEditMode">Subscription Image</label>
                  <input
                    type="file"
                    class="form-control"
                    (change)="onImageSelected($event)"
                    accept="image/jpg,image/jpeg,image/png,image/webp"
                    [class.is-invalid]="isFieldInvalid('image')">
                  <div class="form-text">Supported formats: JPG, JPEG, PNG, WEBP. Max size: 10MB</div>
                  <div class="invalid-feedback" *ngIf="isFieldInvalid('image')">
                    {{ getFieldError('image') }}
                  </div>

                  <!-- Image Preview -->
                  <div class="mt-3" *ngIf="imagePreview">
                    <img [src]="imagePreview" alt="Preview" class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                  </div>
                   <div class="mt-3" *ngIf="!imagePreview && isEditMode">
                    <img [src]="image" alt="Preview" class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                  </div>
                </div>
              </div>

              <!-- Limits & Features -->
              <div class="col-md-6">
                <h5 class="mb-4">Subscription Limits</h5>

                <!-- Max Specialization Scopes -->
                <div class="mb-4">
                  <label class="form-label required">Max Specialization Scopes</label>
                  <input
                    type="number"
                    class="form-control"
                    formControlName="maxSpecializationScopes"
                    [class.is-invalid]="isFieldInvalid('maxSpecializationScopes')"
                    placeholder="0"
                    min="0">
                  <div class="invalid-feedback" *ngIf="isFieldInvalid('maxSpecializationScopes')">
                    {{ getFieldError('maxSpecializationScopes') }}
                  </div>
                </div>

                <!-- Max Specializations -->
                <div class="mb-4">
                  <label class="form-label required">Max Specializations</label>
                  <input
                    type="number"
                    class="form-control"
                    formControlName="maxSpecializations"
                    [class.is-invalid]="isFieldInvalid('maxSpecializations')"
                    placeholder="0"
                    min="0">
                  <div class="invalid-feedback" *ngIf="isFieldInvalid('maxSpecializations')">
                    {{ getFieldError('maxSpecializations') }}
                  </div>
                </div>

                <!-- Max Locations -->
                <div class="mb-4">
                  <label class="form-label required">Max Locations</label>
                  <input
                    type="number"
                    class="form-control"
                    formControlName="maxLocations"
                    [class.is-invalid]="isFieldInvalid('maxLocations')"
                    placeholder="0"
                    min="0">
                  <div class="invalid-feedback" *ngIf="isFieldInvalid('maxLocations')">
                    {{ getFieldError('maxLocations') }}
                  </div>
                </div>

                <!-- Max Advertisements -->
                <div class="mb-4">
                  <label class="form-label required">Max Advertisements</label>
                  <input
                    type="number"
                    class="form-control"
                    formControlName="maxAdvertisements"
                    [class.is-invalid]="isFieldInvalid('maxAdvertisements')"
                    placeholder="0"
                    min="0">
                  <div class="invalid-feedback" *ngIf="isFieldInvalid('maxAdvertisements')">
                    {{ getFieldError('maxAdvertisements') }}
                  </div>
                </div>

                <!-- Max Operations -->
                <div class="mb-4">
                  <label class="form-label required">Max Operations</label>
                  <input
                    type="number"
                    class="form-control"
                    formControlName="maxOperations"
                    [class.is-invalid]="isFieldInvalid('maxOperations')"
                    placeholder="0"
                    min="0">
                  <div class="invalid-feedback" *ngIf="isFieldInvalid('maxOperations')">
                    {{ getFieldError('maxOperations') }}
                  </div>
                </div>

                <!-- Special Advertisements Count -->
                <div class="mb-4">
                  <label class="form-label required">Special Advertisements Count</label>
                  <input
                    type="number"
                    class="form-control"
                    formControlName="specialAdvertisementsCount"
                    [class.is-invalid]="isFieldInvalid('specialAdvertisementsCount')"
                    placeholder="0"
                    min="0">
                  <div class="invalid-feedback" *ngIf="isFieldInvalid('specialAdvertisementsCount')">
                    {{ getFieldError('specialAdvertisementsCount') }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="row mt-5">
              <div class="col-12">
                <div class="d-flex justify-content-end gap-3">
                  <button type="button" class="btn btn-secondary" (click)="onCancel()">
                    <i class="fas fa-times me-2"></i>
                    Cancel
                  </button>
                  <button type="submit" class="btn btn-primary" [disabled]="subscriptionForm.invalid">
                    <i class="fas fa-save me-2"></i>
                    {{ isEditMode ? 'Update Subscription' : 'Create Subscription' }}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
