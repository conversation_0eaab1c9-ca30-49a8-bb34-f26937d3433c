import { Injectable } from '@angular/core';
import { ConfigFactoryService } from './services/config-factory.service';
import { InputConfig, StepperConfiguration } from './services/base-config.service';

@Injectable({
  providedIn: 'root'
})
export class StepperInputConfigService {

  constructor(private configFactory: ConfigFactoryService) { }

  /**
   * Get input configurations for the stepper modal
   */
  getInputConfigs(stepperModal: any): StepperConfiguration[] {
    return this.configFactory.getInputConfigs(stepperModal);
  }

  /**
   * Get inputs for a specific configuration key and step
   */
  getInputsForKey(key: string, step: number, stepperModal: any): InputConfig[] {
    return this.configFactory.getInputsForKey(key, step, stepperModal);
  }

  /**
   * Check if a configuration key exists
   */
  hasConfiguration(key: string, stepperModal: any): boolean {
    return this.configFactory.hasConfiguration(key, stepperModal);
  }

  /**
   * Get all available configuration keys
   */
  getAvailableConfigKeys(stepperModal: any): string[] {
    return this.configFactory.getAllAvailableConfigKeys(stepperModal);
  }

  /**
   * Get configuration type based on key
   */
  getConfigurationType(key: string): 'sell' | 'purchase' | 'rental' | 'rentals_outside_compound' | 'sell_outside_compound' | 'purchase_outside_compound' | 'unknown' {
    return this.configFactory.getConfigurationType(key);
  }
}
