<div class="dropdown" [class.show]="isOpen" (clickOutside)="closeDropdown()">
  <button class="btn btn-md btn-light-dark-blue me-3 cursor-pointer dropdown-toggle"
      (click)="toggleDropdown($event)"
      aria-expanded="isOpen">
    <i class="fa-solid fa-filter fs-3"></i>
    {{ selectedMainOption || placeholder }}
  </button>

  <div class="dropdown-menu dropdown-menu-left p-0" [class.show]="isOpen">
    <div class="card shadow-sm" style="min-width: 350px;">
      <div class="card-header border-0 pt-5">
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold fs-3 mb-1">Filters</span>
        </h3>
      </div>

      <div class="card-body pt-0">
        <form [formGroup]="form" (ngSubmit)="onSubmit()">
          <!-- Specialization dropdown -->
          <div class="mb-5">
            <label for="specializationScope" class="form-label fs-6 fw-semibold text-gray-700">Specialization Scope</label>
            <select id="specializationScope" formControlName="specializationScope"
                    class="form-select form-select-solid" data-control="select2" data-hide-search="true">
              <option value="">Select...</option>
              <option *ngFor="let option of options.specializationScope" [value]="option">
                {{ option }}
              </option>
            </select>
          </div>

          <!-- Status dropdown -->
          <div class="mb-5">
            <label for="status" class="form-label fs-6 fw-semibold text-gray-700">Status</label>
            <select id="status" formControlName="status"
                    class="form-select form-select-solid" data-control="select2" data-hide-search="true">
              <option value="">Select...</option>
              <option *ngFor="let option of options.status" [value]="option">
                {{ option }}
              </option>
            </select>
          </div>

          <!-- Type dropdown -->
          <div class="mb-7">
            <label for="type" class="form-label fs-6 fw-semibold text-gray-700">Type</label>
            <select id="type" formControlName="type"
                    class="form-select form-select-solid" data-control="select2" data-hide-search="true">
              <option value="">Select...</option>
              <option *ngFor="let option of options.type" [value]="option">
                {{ option }}
              </option>
            </select>
          </div>

          <!-- Form actions -->
          <div class="d-flex justify-content-end gap-3">
            <button type="button" class="btn btn-light" (click)="closeDropdown()">
              Cancel
            </button>
            <button type="submit" class="btn btn-light-dark-blue">
              <span class="indicator-label">Apply</span>
              <span class="indicator-progress">
                Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
