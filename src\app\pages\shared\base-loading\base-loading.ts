import { BehaviorSubject, Subscription } from 'rxjs';
import { Injectable, OnDestroy } from '@angular/core';

/**
 * BaseLoading class provides loading state management functionality
 * that can be extended by components that need loading state.
 */
@Injectable()
export class BaseLoading implements OnDestroy {
  /**
   * BehaviorSubject to track loading state
   * Can be used with async pipe in templates
   */
  isLoading$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  /**
   * Boolean property to track loading state
   * Can be used directly in templates with *ngIf
   */
  isLoading: boolean = false;

  /**
   * Array to store subscriptions for cleanup
   */
  protected subscriptions: Subscription[] = [];

  constructor() {
    // Subscribe to the BehaviorSubject to update the boolean property
    const loadingSubscription = this.isLoading$
      .asObservable()
      .subscribe((value) => (this.isLoading = value));

    this.subscriptions.push(loadingSubscription);
  }

  /**
   * Set loading state to true
   */
  startLoading(): void {
    this.isLoading$.next(true);
  }

  /**
   * Set loading state to false
   */
  stopLoading(): void {
    this.isLoading$.next(false);
  }

  /**
   * Set loading state to a specific value
   * @param state - The loading state to set
   */
  setLoadingState(state: boolean): void {
    this.isLoading$.next(state);
  }

  /**
   * Clean up subscriptions when component is destroyed
   */
  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }
}
