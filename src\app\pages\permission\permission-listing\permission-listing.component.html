<div class="card">
    <div class="card-header border-0 pt-6">
        <!--begin::Card title-->
        <div class="card-title">
            <!--begin::Search-->
            <div class="d-flex align-items-center position-relative my-1">
                <app-keenicon name="magnifier" class="fs-3 position-absolute ms-5"></app-keenicon>
                <input type="text" data-action="filter" class="form-control form-control-solid w-250px ps-12" placeholder="Search Users">
            </div>
            <!--end::Search-->
        </div>
        <!--begin::Card title-->

        <!--begin::Card toolbar-->
        <div class="card-toolbar">
            <!--begin::Toolbar-->
            <div class="d-flex justify-content-end">
                <!--begin::Add permission-->
                <button type="button" class="btn btn-primary" data-action="create">
                    Add Permissions
                </button>
                <!--end::Add permission-->
            </div>
            <!--end::Toolbar-->
        </div>
        <!--end::Card toolbar-->
    </div>

    <div class="card-body pt-0">
        <app-crud [datatableConfig]="datatableConfig" route="/apps/users" (deleteEvent)="delete($event)" (editEvent)="edit($event)" (createEvent)="create()" [reload]="reloadEvent" [modal]="formModal"></app-crud>
    </div>
</div>

<swal #noticeSwal [swalOptions]="swalOptions">
</swal>

<ng-template #formModal let-modal>
    <form #myForm="ngForm" (ngSubmit)="onSubmit($event, myForm)">
      <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">Permission Details</h4>
        <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss('Cross click')"></button>
      </div>
      <div class="modal-body">
        <div class="d-flex flex-column scroll-y me-n7 pe-7 mw-650px" id="kt_modal_update_customer_scroll" data-kt-scroll="true" data-kt-scroll-activate="{default: false, lg: true}" data-kt-scroll-max-height="auto" data-kt-scroll-dependencies="#kt_modal_update_customer_header" data-kt-scroll-wrappers="#kt_modal_update_customer_scroll"
          data-kt-scroll-offset="300px">
          <!--begin::User toggle-->
          <div class="fw-bold fs-3 rotate collapsible mb-7" data-bs-toggle="collapse" role="button" (click)="collapse1.toggle()" [attr.aria-expanded]="!isCollapsed1" aria-controls="kt_modal_update_customer_user_info">User Information
            <span class="ms-2 rotate-180">
              <i class="ki-duotone ki-down fs-3" [ngClass]="{ 'ki-up': !isCollapsed1 }"></i>
            </span>
          </div>
          <!--end::User toggle-->
          <!--begin::User form-->
          <div class="collapse show" #collapse1="ngbCollapse" [(ngbCollapse)]="isCollapsed1">
            <!--begin::Input group-->
            <div class="fv-row mb-7">
              <!--begin::Label-->
              <label class="required fw-semibold fs-6 mb-2">Name</label>
              <!--end::Label-->
              <!--begin::Input-->
              <input type="text" class="form-control form-control-solid" name="name" [(ngModel)]="permissionModel.name" #name="ngModel" required minlength="4" />
              <div *ngIf="myForm.submitted && myForm.invalid" class="fv-plugins-message-container fv-plugins-message-container--enabled invalid-feedback">
                <div *ngIf="name.errors?.['required']">Permission name is required</div>
                <div *ngIf="name.errors?.['minlength']">Permission name must be at least 4 characters long</div>
              </div>
              <!--end::Input-->
            </div>
            <!--end::Input group-->
          </div>
          <!--end::User form-->
        </div>
      </div>
      <div class="modal-footer flex-center">
        <!--begin::Button-->
        <button type="reset" id="kt_modal_update_customer_cancel" class="btn btn-light me-3" (click)="modal.dismiss('cancel')">
          Discard
        </button>
        <!--end::Button-->
  
        <!--begin::Button-->
        <button type="submit" id="kt_modal_update_customer_submit" class="btn btn-primary" [attr.data-kt-indicator]="isLoading ? 'on' : 'off'">
          <span class="indicator-label">
            Submit
          </span>
          <span class="indicator-progress">
            Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
          </span>
        </button>
        <!--end::Button-->
      </div>
    </form>
</ng-template>