<div class="card">
  <div class="card-body">
    <div
      class="stepper stepper-links d-flex flex-column pt-15"
      id="kt_create_account_stepper"
    >
      <div class="stepper-nav mb-5">
        <div
          class="stepper-item"
          [ngClass]="{
            current: currentStep$.value === 1,
            completed: currentStep$.value > 1
          }"
        >
          <h3 class="stepper-title">Account Type</h3>
        </div>

        <div
          class="stepper-item"
          [ngClass]="{
            current: currentStep$.value === 2,
            completed: currentStep$.value > 2
          }"
        >
          <h3 class="stepper-title">Account Info</h3>
        </div>

        <div
          class="stepper-item"
          [ngClass]="{
            current: currentStep$.value === 3,
            completed: currentStep$.value > 3
          }"
        >
          <h3 class="stepper-title">Business Info</h3>
        </div>

        <div
          class="stepper-item"
          [ngClass]="{
            current: currentStep$.value === 4,
            completed: currentStep$.value > 4
          }"
        >
          <h3 class="stepper-title">Billing Details</h3>
        </div>

        <div
          class="stepper-item"
          [ngClass]="{ current: currentStep$.value === 5 }"
        >
          <h3 class="stepper-title">Completed</h3>
        </div>
      </div>

      <form
        class="mx-auto mw-600px w-100 pt-15 pb-10"
        noValidate
        id="kt_create_account_form"
      >
        <div class="current">
          <ng-container *ngIf="currentStep$.value === 1">
            <app-step1
              class="w-100"
              [updateParentModel]="updateAccount"
              [defaultValues]="account$.value"
            ></app-step1>
          </ng-container>

          <ng-container *ngIf="currentStep$.value === 2">
            <app-step2
              class="w-100"
              [updateParentModel]="updateAccount"
              [defaultValues]="account$.value"
            ></app-step2>
          </ng-container>

          <ng-container *ngIf="currentStep$.value === 3">
            <app-step3
              class="w-100"
              [updateParentModel]="updateAccount"
              [defaultValues]="account$.value"
            ></app-step3>
          </ng-container>

          <ng-container *ngIf="currentStep$.value === 4">
            <app-step4
              class="w-100"
              [updateParentModel]="updateAccount"
              [defaultValues]="account$.value"
            ></app-step4>
          </ng-container>

          <ng-container *ngIf="currentStep$.value === 5">
            <app-step5 class="w-100"></app-step5>
          </ng-container>
        </div>

        <div class="d-flex flex-stack pt-15">
          <div class="mr-2">
            <ng-container *ngIf="currentStep$.value !== 1">
              <button
                type="button"
                class="btn btn-lg btn-light-primary me-3"
                (click)="prevStep()"
              >
                <app-keenicon name="arrow-left" class="fs-3 ms-4 me-1"></app-keenicon>
                Back
              </button>
            </ng-container>
          </div>

          <ng-container *ngIf="currentStep$.value !== formsCount">
            <div>
              <button
                type="button"
                class="btn btn-lg btn-primary me-3"
                [disabled]="!isCurrentFormValid$.value"
                (click)="nextStep()"
              >
                <span class="indicator-label">
                  <ng-container *ngIf="currentStep$.value < formsCount - 1">
                    Continue {{ " " }}
                    <app-keenicon name="arrow-right" class="fs-3 ms-2 me-0"></app-keenicon>
                  </ng-container>
                  <ng-container *ngIf="currentStep$.value === formsCount - 1">
                    Submit
                  </ng-container>
                </span>
              </button>
            </div>
          </ng-container>
        </div>
      </form>
    </div>
  </div>
</div>
