import { Validators } from '@angular/forms';

// Option types interface
export interface OptionItem {
  key: string;
  value: string;
}

// Step configuration constants
export const STEPPER_CONFIG = {
  TOTAL_STEPS: 5,
  INITIAL_STEP: 1,
  STEP_NAMES: {
    1: 'Request Settings',
    2: 'Location Information',
    3: 'Unit Information',
    4: 'Project Documents',
    5: 'Financial Information',
  } as { [key: number]: string }
};

// Main option arrays
export const SPECIALIZATION_SCOPE_OPTIONS: OptionItem[] = [
  { key: 'Purchase/ Sell Outside Compound', value: 'purchase_sell_outside_compound' },
  { key: 'Primary Inside Compound', value: 'primary_inside_compound' },
  { key: 'Resale Inside Compound', value: 'resale_inside_compound' },
  { key: 'Rentals Outside Compound', value: 'rentals_outside_compound' },
  { key: 'Rentals Inside Compound', value: 'rentals_inside_compound' },
];

export const TYPE_OPTIONS: OptionItem[] = [
  { key: 'Purchasing', value: 'purchasing' },
  { key: 'Sell', value: 'sell' },
  { key: 'Rent Out', value: 'rent_out' },
  { key: 'Rent In', value: 'rent_in' },
];

// Property type options
export const FLOOR_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Ground Floor', value: 'ground' },
  { key: 'Last Floor', value: 'last_floor' },
  { key: 'Repeated', value: 'repeated' },
  { key: 'All The Above Are Suitable', value: 'all_the_above_are_suitable' },
];

export const SELL_FLOOR_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Ground Floor', value: 'ground' },
  { key: 'Last Floor', value: 'last_floor' },
  { key: 'Repeated', value: 'repeated' },
];

export const BUILDING_LICENSE_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Permit Available', value: 'Permit_Available' },
  { key: 'No Permit', value: 'No_Permit' },
];

export const UNIT_LAYOUT_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Partial Roof', value: 'partial_roof' },
  { key: 'Full Roof', value: 'full_roof' },
  { key: 'Open Space', value: 'open_space' },
  { key: 'Single Apartment', value: 'single_apartment' },
  { key: 'Two Apartments', value: 'two_apartments' },
  { key: 'All Acceptable', value: 'all_acceptable' },
];

export const ROOF_UNIT_LAYOUT_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Partial Roof', value: 'partial_roof' },
  { key: 'Full Roof', value: 'full_roof' },
  { key: 'Open Space', value: 'open_space' },
  { key: 'Single Apartment', value: 'single_apartment' },
  { key: 'Two Apartments', value: 'two_apartments' },
];

export const Purchase_ROOF_UNIT_LAYOUT_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Partial Roof', value: 'partial_roof' },
  { key: 'Full Roof', value: 'full_roof' },
  { key: 'All Acceptable', value: 'all_acceptable' },
];

export const BASEMENT_UNIT_LAYOUT_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Open Space', value: 'open_space' },
  { key: 'Single Apartment', value: 'single_apartment' },
  { key: 'Two Apartments', value: 'two_apartments' },
];

export const BUILDING_LAYOUT_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Open Space', value: 'open_space' },
  { key: 'Single Apartment', value: 'single_apartment' },
  { key: 'Two Apartments', value: 'two_apartments' },
  { key: 'All Acceptable', value: 'all_acceptable' },
];

export const SELL_OUT_BUILDING_LAYOUT_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Open Space', value: 'open_space' },
  { key: 'Fully Built Warehouse', value: 'fully_built_warehouse' },
  { key: 'Under Construction', value: 'under_construction' },
];

export const RENT_BUILDING_LAYOUT_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Open Space', value: 'open_space' },
  { key: 'Fully Built Warehouse', value: 'fully_built_warehouse' },
  { key: 'All Acceptable', value: 'all_acceptable' },
];

export const Land_OUT_BUILDING_LAYOUT_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Open Space', value: 'open_space' },
  { key: 'Under Construction', value: 'under_construction' },
];

export const GROUND_LAYOUT_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Vacant Land', value: 'vacant_land' },
  { key: 'Under Construction', value: 'under_construction' },
  { key: 'Fully Built', value: 'fully_built' },
  { key: 'All Acceptable', value: 'all_acceptable' },
];

export const UNIT_DESIGN_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Custom Design', value: 'custom_design' },
  { key: 'One Apartment Per Floor', value: 'one_apartment_per_floor' },
  { key: 'Two Apartments Per Floor', value: 'two_apartments_per_floor' },
  { key: 'More Than Two Apartments Per Floor', value: 'more_than_two_apartments_per_floor' },
  { key: 'All Acceptable', value: 'all_acceptable' },
];

export const ACTIVITY_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Administrative Only', value: 'administrative_only' },
  { key: 'Commercial Only', value: 'commercial_only' },
  { key: 'Medical Only', value: 'medical_only' },
  { key: 'Administrative and Commercial', value: 'administrative_and_commercial' },
  { key: 'Administrative, Commercial and Medical', value: 'administrative_commercial_and_medical' },
];

export const UNIT_DESCRIPTION_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Single Front', value: 'single_front' },
  { key: 'Corner', value: 'corner' },
  { key: 'Double Front', value: 'double_front' },
  { key: 'Triple Corner', value: 'triple_corner' },
  { key: 'Quad Corner', value: 'quad_corner' },
  { key: 'All Acceptable', value: 'all_acceptable' },
];

export const SUB_UNIT_TYPE_OPTIONS: OptionItem[] = [
  { key: 'Apartments', value: 'apartments' },
  { key: 'I Villa', value: 'i_villa' },
  { key: 'Twin Houses', value: 'town_houses' },
  { key: 'Administrative Units', value: 'administrative_units' },
  { key: 'Medical Clinics', value: 'medical_clinics' },
  { key: 'Pharmacies', value: 'pharmacies' },
  { key: 'Commercial Stores', value: 'commercial_stores' },
  { key: 'Warehouses', value: 'warehouses' },
  { key: 'Warehouses Lands or Warehouses', value: 'warehouses' },
  { key: 'Factory Lands', value: 'factory_lands' },
  { key: 'Factory Lands or Factory', value: 'factory_lands' },
  { key: 'Warehouses Land', value: 'warehouses_land' },
  { key: 'Standalone Villas', value: 'standalone_villas' },
  { key: 'Commercial Administrative Buildings', value: 'commercial_administrative_buildings' },
  { key: 'Commercial Administrative Lands', value: 'commercial_administrative_lands' },
  { key: 'Residential Buildings', value: 'residential_buildings' },
  { key: 'Residential Lands', value: 'residential_lands' },
  { key: 'Chalets', value: 'chalets' },
  { key: 'Hotels', value: 'hotels' },
  { key: 'Factories', value: 'factories' },
  { key: 'Basements', value: 'basements' },
  { key: 'Full Buildings', value: 'full_buildings' },
  { key: 'Commercial Units', value: 'commercial_units' },
  { key: 'Shops', value: 'shops' },
  { key: 'Mixed Housings', value: 'mixed_housings' },
  { key: 'Cooperatives', value: 'cooperatives' },
  { key: 'Youth Units', value: 'youth_units' },
  { key: 'Ganat Misr', value: 'ganat_misr' },
  { key: 'Dar Misr', value: 'dar_misr' },
  { key: 'Sakan Misr', value: 'sakan_misr' },
  { key: 'Industrial Lands', value: 'industrial_lands' },
  { key: 'Cabin', value: 'cabin' },
  { key: 'Vacation Villa', value: 'vacation_villa' },
  { key: 'Hotel Units', value: 'hotel_units' },
];

export const RENT_RECURRENCE_OPTIONS: OptionItem[] = [
  { key: 'Monthly', value: 'monthly' },
  { key: 'Quarterly', value: 'quarterly' },
  { key: 'Semi Annually', value: 'semi_annually' },
  { key: 'Annually', value: 'annually' },
];

export const RENT_DURATION_OPTIONS: OptionItem[] = [
  { key: 'Monthly', value: 'monthly' },
  { key: 'Daily', value: 'daily' },
];

export const Hotels_SUB_UNIT_OPTIONS: OptionItem[] = [
  { key: 'Apartments', value: 'apartments' },
  { key: 'Duplexes', value: 'duplexes' },
  { key: 'Studios', value: 'studios' },
  { key: 'Penthouses', value: 'penthouses' },
  { key: 'Villas', value: 'villas' },
  { key: 'Basements', value: 'basements' },
  { key: 'Roofs', value: 'roofs' },
  { key: 'Standalone Villas', value: 'standalone_villas' },

];

export const SUB_UNIT_OPTIONS: OptionItem[] = [
  { key: 'Cabin', value: 'cabin' },
  { key: 'Chalets', value: 'chalets' },
  { key: 'Vacation Villa', value: 'vacation_villa' },
];
export const PAYMENT_METHOD_OPTIONS: OptionItem[] = [
  { key: 'Cash', value: 'cash' },
  { key: 'Installment', value: 'installment' },
  { key: 'Both', value: 'all_of_the_above_are_suitable' },
];

export const REQUIRED_INSURANCE_TYPES_OPTIONS: OptionItem[] = [
  { key: 'One Month', value: 'one_month' },
  { key: 'Two Months', value: 'two_months' },
  { key: 'Fixed Amount', value: 'fixed_amount' },
];

export const OTHER_EXPENSES_OPTIONS: OptionItem[] = [
  { key: 'Other', value: 'other' },
  { key: 'Electricity', value: 'electricity' },
  { key: 'Gas', value: 'gas' },
  { key: 'Water', value: 'water' },
  { key: 'Security Maintenance', value: 'security_maintenance' },
];

export const FURNISHING_STATUS_OPTIONS: OptionItem[] = [
  { key: 'Unfurnished', value: 'unfurnished' },
  { key: 'Furnished with Air Conditioners', value: 'furnished_with_air_conditioners' },
  { key: 'Furnished without Air Conditioners', value: 'furnished_without_air_conditioners' },
];

export const ALL_FURNISHING_STATUS_OPTIONS: OptionItem[] = [
  { key: 'Unfurnished', value: 'unfurnished' },
  { key: 'Furnished with Air Conditioners', value: 'furnished_with_air_conditioners' },
  { key: 'Furnished without Air Conditioners', value: 'furnished_without_air_conditioners' },
];

export const DELIVERY_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Immediate Delivery', value: 'immediate_delivery' },
  { key: 'Under Construction', value: 'under_construction' },
];

export const Land_DELIVERY_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Delivered', value: 'delivered' },
  { key: 'Not Delivered Yet', value: 'not_delivered_yet' },
];

export const Purchase_Land_DELIVERY_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Delivered', value: 'delivered'},
  { key: 'Not Delivered Yet', value: 'not_delivered_yet'},
  { key: 'All Of The Above', value: 'all_of_the_above'},
];

export const PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Immediate Delivery', value: 'immediate_delivery' },
  { key: 'Under Construction', value: 'under_construction' },
  { key: 'All Of The Above', value: 'all_of_the_above' },
];

export const BUILDING_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Fully Constructed', value: 'fully_constructed' },
  { key: 'Under Construction', value: 'under_construction' },
];

export const Purchase_BUILDING_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Fully Constructed', value: 'fully_constructed' },
  { key: 'Under Construction', value: 'under_construction' },
  { key: 'All Of Above', value: 'all_acceptable' },
];

export const FINANCIAL_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Paid in Full', value: 'paid_in_full' },
  { key: 'Partially Paid with Remaining Installments', value: 'partially_paid_with_remaining_installments' },
];

export const LEGAL_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Licensed', value: 'licensed' },
  { key: 'Reconciled', value: 'reconciled' },
  { key: 'Reconciliation Required', value: 'reconciliation_required' },
];

export const FIT_OUT_CONDITION_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Unfitted', value: 'unfitted' },
  { key: 'Fully Fitted', value: 'fully_fitted' },
  { key: 'All The Above Are Suitable', value: 'all_the_above_are_suitable' },
];

export const Sell_FIT_OUT_CONDITION_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Unfitted', value: 'unfitted' },
  { key: 'Fully Fitted', value: 'fully_fitted' },
  { key: 'All The Above Are Suitable', value: 'all_the_above_are_suitable' },
];

export const FINISHING_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'On Brick', value: 'on_brick' },
  { key: 'Semi Finished', value: 'semi_finished' },
  { key: 'Company Finished', value: 'company_finished' },
  { key: 'Super Lux', value: 'super_lux' },
  { key: 'Ultra Super Lux', value: 'ultra_super_lux' },
];
export const PHARMACY_FINISHING_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Semi Finished', value: 'semi_finished' },
  { key: 'Full Finished', value: 'full_finished' },
];

export const BUILD_RENT_FINISHING_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Semi Finished', value: 'semi_finished' },
  { key: 'Full Finished', value: 'full_finished' },
  { key: 'All Of The Above', value: 'all_of_the_above' },
];

export const RENTAL_FINISHING_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Full Finished', value: 'Full_finished' },
  { key: 'Super Lux', value: 'super_lux' },
  { key: 'Ultra Super Lux', value: 'ultra_super_lux' },
];

export const RENTALIN_FINISHING_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Full Finished', value: 'Full_finished' },
  { key: 'Super Lux', value: 'super_lux' },
  { key: 'Ultra Super Lux', value: 'ultra_super_lux' },
  { key: 'All Of The Above', value: 'all_of_the_above' },
];

export const PURCHASE_FINISHING_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'On Brick', value: 'on_brick' },
  { key: 'Semi Finished', value: 'semi_finished' },
  { key: 'Full Finished', value: 'Full_finished' },
  { key: 'Company Finished', value: 'company_finished' },
  { key: 'Super Lux', value: 'super_lux' },
  { key: 'Ultra Super Lux', value: 'ultra_super_lux' },
  { key: 'All Of The Above', value: 'all_of_the_above' },
];

export const SPECIFIC_FINISHING_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'On Brick', value: 'on_brick' },
  { key: 'Semi Finished', value: 'semi_finished' },
  { key: 'Full Finished', value: 'full_finished' },
];

export const Purchase_SPECIFIC_FINISHING_STATUS_TYPES_OPTIONS: OptionItem[] = [
  { key: 'On Brick', value: 'on_brick' },
  { key: 'Semi Finished', value: 'semi_finished' },
  { key: 'Full Finished', value: 'full_finished' },
  { key: 'All Of The Above', value: 'all_of_the_above' },
];

export const UNIT_VIEW_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Water View', value: 'water_view' },
  { key: 'Gardens and Landscape', value: 'gardens_and_landscape' },
  { key: 'Street', value: 'street' },
  { key: 'Entertainment Area', value: 'entertainment_area' },
  { key: 'Garden', value: 'garden' },
  { key: 'Main Street', value: 'main_street' },
  { key: 'Square', value: 'square' },
  { key: 'Side Street', value: 'side_street' },
  { key: 'Rear View', value: 'rear_view' },
];

export const PURCHASE_UNIT_VIEW_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Water View', value: 'water_view' },
  { key: 'Gardens and Landscape', value: 'gardens_and_landscape' },
  { key: 'Street', value: 'street' },
  { key: 'Entertainment Area', value: 'entertainment_area' },
  { key: 'Garden', value: 'garden' },
  { key: 'Main Street', value: 'main_street' },
  { key: 'Square', value: 'square' },
  { key: 'Side Street', value: 'side_street' },
  { key: 'Rear View', value: 'rear_view' },
  { key: 'All Of The Above', value: 'all_of_the_above' },
];

export const Rental_UNIT_VIEW_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Water View', value: 'water_view' },
  { key: 'Gardens and Landscape', value: 'gardens_and_landscape' },
  { key: 'Street', value: 'street' },
  { key: 'Entertainment Area', value: 'entertainment_area' },
];

export const PURCHASE_VACATION_UNIT_VIEW_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Water View', value: 'water_view' },
  { key: 'Gardens and Landscape', value: 'gardens_and_landscape' },
  { key: 'Garden', value: 'garden' },
  { key: 'Main Street', value: 'main_street' },
  { key: 'Side Street', value: 'side_street' },
  { key: 'All Of The Above', value: 'all_of_the_above' },
];

export const SELL_VACATION_UNIT_VIEW_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Water View', value: 'water_view' },
  { key: 'Gardens and Landscape', value: 'gardens_and_landscape' },
  { key: 'Garden', value: 'garden' },
  { key: 'Main Street', value: 'main_street' },
  { key: 'Side Street', value: 'side_street' },
];

export const UNIT_FACING_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Right of Facade', value: 'right_of_facade' },
  { key: 'Left of Facade', value: 'left_of_facade' },
  { key: 'Side View', value: 'side_view' },
  { key: 'Rear View', value: 'rear_view' },
];

export const OTHER_ACCESSORIES_OPTIONS: OptionItem[] = [
  { key: 'Garage', value: 'garage' },
  { key: 'Clubhouse', value: 'clubhouse' },
  { key: 'Club', value: 'club' },
  { key: 'Storage', value: 'storage' },
  { key: 'Elevator', value: 'elevator' },
  { key: 'Swimming Pool', value: 'swimming_pool' },
];

export const SPECIFIC_OTHER_ACCESSORIES_OPTIONS: OptionItem[] = [
  { key: 'Garage', value: 'garage' },
  { key: 'Storage', value: 'storage' },
  { key: 'Elevator', value: 'elevator' },
  { key: 'Swimming Pool', value: 'swimming_pool' },
];

export const Purchase_OTHER_ACCESSORIES_OPTIONS: OptionItem[] = [
  { key: 'Garage', value: 'garage' },
  { key: 'Storage', value: 'storage' },
  { key: 'Elevator', value: 'elevator' },
];

export const SELL_OUT_OTHER_ACCESSORIES_OPTIONS: OptionItem[] = [
  { key: 'Garage', value: 'garage' },
  { key: 'Storage', value: 'storage' },
  { key: 'Elevator', value: 'elevator' },
  { key: 'Land Share', value: 'land_share' },
];

export const BUILDING_DEADLINE_TYPES_OPTIONS: OptionItem[] = [
  { key: 'Grace Period Allowed', value: 'grace_period_allowed' },
  { key: 'No Grace Period', value: 'no_grace_period' },
];

// File validation constants
export const FILE_VALIDATION_CONFIG = {
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif'],
  MAX_SIZE: 10 * 1024 * 1024 // 10MB
};
