import { Component, OnInit, ChangeDetectorRef } from '@angular/core';

@Component({
  selector: 'app-developers-dashboard',
  templateUrl: './developers-dashboard.component.html',
  styleUrls: ['./developers-dashboard.component.scss']
})
export class DevelopersDashboardComponent implements OnInit {

  // Developer statistics
  totalDevelopers: number = 0;
  activeDevelopers: number = 0;
  totalDeveloperUnits: number = 0;
  averagePaymentPeriod: number = 0;

  // Top developers
  topDevelopers: any[] = [];

  // Best selling projects
  bestSellingProjects: any[] = [];

  // Payment period statistics
  paymentPeriodStats: any[] = [];

  // Developer performance
  developerPerformance: any[] = [];

  // Project status statistics
  projectStatusStats: any = {
    completed: 0,
    underConstruction: 0,
    planning: 0,
    onHold: 0
  };

  // Revenue statistics
  revenueStats: any = {
    totalRevenue: 0,
    monthlyRevenue: 0,
    averageProjectValue: 0,
    topRevenueProject: ''
  };

  constructor(private cd: ChangeDetectorRef) {}

  ngOnInit() {
    this.loadDeveloperStatistics();
    this.loadTopDevelopers();
    this.loadBestSellingProjects();
    this.loadPaymentPeriodStats();
    this.loadDeveloperPerformance();
    this.loadProjectStatusStats();
    this.loadRevenueStats();
  }

  loadDeveloperStatistics() {
    // Mock data - replace with actual API calls
    this.totalDevelopers = 45;
    this.activeDevelopers = 38;
    this.totalDeveloperUnits = 1890;
    this.averagePaymentPeriod = 24; // months

    this.cd.detectChanges();
  }

  loadTopDevelopers() {
    // Mock data for top developers
    this.topDevelopers = [
      {
        name: 'شركة إعمار مصر',
        projects: 12,
        units: 450,
        revenue: 2800000000,
        rating: 4.8,
        logo: 'assets/media/developers/emaar.png'
      },
      {
        name: 'شركة بالم هيلز',
        projects: 8,
        units: 320,
        revenue: 1950000000,
        rating: 4.6,
        logo: 'assets/media/developers/palm-hills.png'
      },
      {
        name: 'شركة الأهرام للتطوير',
        projects: 15,
        units: 380,
        revenue: 2100000000,
        rating: 4.5,
        logo: 'assets/media/developers/ahram.png'
      },
      {
        name: 'شركة المقاولون العرب',
        projects: 10,
        units: 290,
        revenue: 1750000000,
        rating: 4.4,
        logo: 'assets/media/developers/arab-contractors.png'
      },
      {
        name: 'شركة حسن علام',
        projects: 6,
        units: 180,
        revenue: 1200000000,
        rating: 4.3,
        logo: 'assets/media/developers/hassan-allam.png'
      }
    ];

    this.cd.detectChanges();
  }

  loadBestSellingProjects() {
    // Mock data for best selling projects
    this.bestSellingProjects = [
      {
        name: 'كمبوند الموندو',
        developer: 'شركة إعمار مصر',
        location: 'القاهرة الجديدة',
        totalUnits: 450,
        soldUnits: 380,
        averagePrice: 3200000,
        completionDate: '2025-12-01',
        salesPercentage: 84
      },
      {
        name: 'كمبوند بالم هيلز',
        developer: 'شركة بالم هيلز',
        location: 'الشيخ زايد',
        totalUnits: 320,
        soldUnits: 250,
        averagePrice: 4100000,
        completionDate: '2026-06-01',
        salesPercentage: 78
      },
      {
        name: 'العاصمة الجديدة',
        developer: 'شركة المقاولون العرب',
        location: 'العاصمة الإدارية',
        totalUnits: 280,
        soldUnits: 210,
        averagePrice: 5200000,
        completionDate: '2027-03-01',
        salesPercentage: 75
      },
      {
        name: 'كمبوند الأهرام',
        developer: 'شركة الأهرام للتطوير',
        location: 'أكتوبر',
        totalUnits: 380,
        soldUnits: 270,
        averagePrice: 2800000,
        completionDate: '2025-09-01',
        salesPercentage: 71
      }
    ];

    this.cd.detectChanges();
  }

  loadPaymentPeriodStats() {
    // Mock data for payment period statistics
    this.paymentPeriodStats = [
      { period: '12 شهر', count: 8, percentage: 18 },
      { period: '18 شهر', count: 12, percentage: 27 },
      { period: '24 شهر', count: 15, percentage: 33 },
      { period: '36 شهر', count: 7, percentage: 16 },
      { period: '48 شهر', count: 3, percentage: 6 }
    ];

    this.cd.detectChanges();
  }

  loadDeveloperPerformance() {
    // Mock data for developer performance
    this.developerPerformance = [
      {
        name: 'شركة إعمار مصر',
        onTimeDelivery: 95,
        customerSatisfaction: 4.8,
        qualityRating: 4.7,
        salesVelocity: 85
      },
      {
        name: 'شركة بالم هيلز',
        onTimeDelivery: 88,
        customerSatisfaction: 4.6,
        qualityRating: 4.5,
        salesVelocity: 78
      },
      {
        name: 'شركة الأهرام للتطوير',
        onTimeDelivery: 92,
        customerSatisfaction: 4.5,
        qualityRating: 4.4,
        salesVelocity: 71
      },
      {
        name: 'شركة المقاولون العرب',
        onTimeDelivery: 85,
        customerSatisfaction: 4.4,
        qualityRating: 4.3,
        salesVelocity: 75
      }
    ];

    this.cd.detectChanges();
  }

  loadProjectStatusStats() {
    // Mock data for project status
    this.projectStatusStats = {
      completed: 28,
      underConstruction: 34,
      planning: 15,
      onHold: 8
    };

    this.cd.detectChanges();
  }

  loadRevenueStats() {
    // Mock data for revenue statistics
    this.revenueStats = {
      totalRevenue: 15800000000,
      monthlyRevenue: 890000000,
      averageProjectValue: 185000000,
      topRevenueProject: 'كمبوند الموندو'
    };

    this.cd.detectChanges();
  }

  formatPrice(price: number): string {
    if (price >= 1000000000) {
      return (price / 1000000000).toFixed(1) + ' مليار جنيه';
    } else if (price >= 1000000) {
      return (price / 1000000).toFixed(1) + ' مليون جنيه';
    } else if (price >= 1000) {
      return (price / 1000).toFixed(0) + ' ألف جنيه';
    }
    return price.toLocaleString() + ' جنيه';
  }

  formatNumber(num: number): string {
    return num.toLocaleString();
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'completed': return 'success';
      case 'underConstruction': return 'primary';
      case 'planning': return 'warning';
      case 'onHold': return 'danger';
      default: return 'secondary';
    }
  }

  getPerformanceColor(value: number): string {
    if (value >= 90) return 'success';
    if (value >= 80) return 'primary';
    if (value >= 70) return 'warning';
    return 'danger';
  }

  getRatingStars(rating: number): string[] {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push('fas fa-star text-warning');
    }

    if (hasHalfStar) {
      stars.push('fas fa-star-half-alt text-warning');
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push('far fa-star text-muted');
    }

    return stars;
  }
}
