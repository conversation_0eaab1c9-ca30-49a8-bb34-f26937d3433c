import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { HomeService } from '../services/home.service';
import { TranslationService } from '../../../modules/i18n/translation.service';
import { TranslateService } from '@ngx-translate/core';
import { PropertyTranslationService } from '../../../shared/services/property-translation.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-all-properties',
  templateUrl: './all-properties.component.html',
  styleUrls: ['./all-properties.component.scss']
})
export class AllPropertiesComponent implements OnInit {
  properties: any[] = [];
  isLoading: boolean = false;
  currentPage: number = 0;
  limit: number = 4;
  hasMoreProperties: boolean = true;
  searchText: string = '';
  filteredProperties: any[] = [];
  currentLang: string = 'en';

  constructor(
    private homeService: HomeService,
    private cd: ChangeDetectorRef,
    private router: Router,
    private translationService: TranslationService,
    private translateService: TranslateService,
    private propertyTranslationService: PropertyTranslationService
  ) {}

  ngOnInit(): void {
    this.loadProperties();

    // Get current language
    this.currentLang = this.translationService.getCurrentLanguage();

    // Subscribe to language changes
    this.translationService.currentLanguage$.subscribe(lang => {
      this.currentLang = lang;
      this.cd.detectChanges();
    });
  }

  loadProperties(loadMore: boolean = false): void {
    this.isLoading = true;

    const offset = loadMore ? this.currentPage * this.limit : 0;

    this.homeService.getFeaturedProperties(this.limit, offset).subscribe({
      next: (response: any) => {
        console.log('Properties response:', response);

        if (loadMore) {
           this.properties = [...this.properties, ...(response.data || [])];
        } else {
           this.properties = response.data || [];
        }

        // Update filtered properties after loading
        this.applySearchFilter();

        if (loadMore) {
          this.currentPage++;
        }

        this.isLoading = false;
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading properties:', error);
        this.isLoading = false;
        this.cd.detectChanges();
      }
    });
  }

  loadMoreProperties(): void {
    if (!this.isLoading && this.hasMoreProperties) {
      this.loadProperties(true);
    }
  }

  onPropertyClick(property: any): void {
    const authToken = localStorage.getItem('authToken');
    const currentUserData = localStorage.getItem('currentUser');

    if (!authToken || !currentUserData) {
      Swal.fire({
        title: this.translateService.instant('HOME.ALL_PROPERTIES.LOGIN_REQUIRED'),
        text: this.translateService.instant('HOME.ALL_PROPERTIES.LOGIN_MESSAGE'),
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: this.translateService.instant('HOME.ALL_PROPERTIES.LOGIN_BUTTON'),
        cancelButtonText: this.translateService.instant('HOME.ALL_PROPERTIES.CANCEL_BUTTON'),
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#e74c3c'
      }).then((result) => {
        if (result.isConfirmed) {
          this.router.navigate(['/authentication/login']);
        }
      });
      return;
    }
    try {
      const currentUser = JSON.parse(currentUserData);
      if (!currentUser || !currentUser.role || currentUser.role.trim() === '') {
        Swal.fire({
          title: this.translateService.instant('HOME.ALL_PROPERTIES.LOGIN_REQUIRED'),
          text: this.translateService.instant('HOME.ALL_PROPERTIES.LOGIN_ROLE_MESSAGE'),
          icon: 'warning',
          showCancelButton: true,
          confirmButtonText: this.translateService.instant('HOME.ALL_PROPERTIES.LOGIN_BUTTON'),
          cancelButtonText: this.translateService.instant('HOME.ALL_PROPERTIES.CANCEL_BUTTON'),
          confirmButtonColor: '#28a745',
          cancelButtonColor: '#e74c3c'
        }).then((result) => {
          if (result.isConfirmed) {
            this.router.navigate(['/authentication/login']);
          }
        });
        return;
      }
    } catch (error) {
      // If parsing fails, redirect to login
      Swal.fire({
        title: this.translateService.instant('HOME.ALL_PROPERTIES.LOGIN_REQUIRED'),
        text: this.translateService.instant('HOME.ALL_PROPERTIES.INVALID_SESSION'),
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: this.translateService.instant('HOME.ALL_PROPERTIES.LOGIN_BUTTON'),
        cancelButtonText: this.translateService.instant('HOME.ALL_PROPERTIES.CANCEL_BUTTON'),
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#e74c3c'
      }).then((result) => {
        if (result.isConfirmed) {
          this.router.navigate(['/authentication/login']);
        }
      });
      return;
    }

    // If user is logged in and has role, navigate to property details
    this.router.navigate(['developer/projects/models/units/details'], {
      queryParams: {
        unitId: property.id
      }
    });
  }

  formatPrice(property: any): string {
    const currency = this.translateService.instant('HOME.CURRENCY.EGP');
    const priceOnRequest = this.currentLang === 'ar' ? 'السعر عند الطلب' : 'Price on request';
    const monthText = this.currentLang === 'ar' ? 'شهرياً' : 'month';

    if (property.unitOperation === 'Rent') {
      if (property.monthlyRent) {
        const formattedRent = this.currentLang === 'ar'
          ? this.convertToArabicNumbers(property.monthlyRent.toLocaleString())
          : property.monthlyRent.toLocaleString();
        return `${formattedRent} ${currency}/${monthText}`;
      } else {
        return priceOnRequest;
      }
    } else {
      const price = property.totalPriceInCash || property.totalPriceInInstallment;
      if (price) {
        const formattedPrice = this.currentLang === 'ar'
          ? this.convertToArabicNumbers(price.toLocaleString())
          : price.toLocaleString();
        return `${formattedPrice} ${currency}`;
      } else {
        return priceOnRequest;
      }
    }
  }

  getPropertyType(property: any): string {
    return this.getTranslatedPropertyType(property.type);
  }

  getPropertyLocation(property: any): string {
    if (this.currentLang === 'en') {
      return property.area?.name_en || property.city?.name_en || property.area?.name_ar || property.city?.name_ar || 'Location not specified';
    } else {
      return property.area?.name_ar || property.city?.name_ar || property.area?.name_en || property.city?.name_en || 'الموقع غير محدد';
    }
  }

  getPropertyImage(property: any): string {
    return property.gallery?.[0]?.url || 'assets/media/auth/404-error.png';
  }

  goBack(): void {
    this.router.navigate(['/home']);
  }

  onSearchChange(): void {
    this.applySearchFilter();
  }

  applySearchFilter(): void {
    if (!this.searchText || this.searchText.trim() === '') {
      this.filteredProperties = [...this.properties];
    } else {
      const searchTerm = this.searchText.toLowerCase().trim();
      this.filteredProperties = this.properties.filter(property => {
        // Search in property type
        const type = (property.type || '').toLowerCase();

        // Search in location (area, city, subArea)
        const location = this.getPropertyLocation(property).toLowerCase();

        // Search in compound type
        const compoundType = (property.compoundType || '').toLowerCase();

        // Search in unit operation (Rent, Sale, etc.)
        const operation = (property.unitOperation || '').toLowerCase();

        // Search in number of rooms
        const rooms = (property.numberOfRooms || '').toString();

        // Search in number of bathrooms
        const bathrooms = (property.numberOfBathrooms || '').toString();

        return type.includes(searchTerm) ||
               location.includes(searchTerm) ||
               compoundType.includes(searchTerm) ||
               operation.includes(searchTerm) ||
               rooms.includes(searchTerm) ||
               bathrooms.includes(searchTerm);
      });
    }
    this.cd.detectChanges();
  }

  // ترجمة عمليات العقار
  getTranslatedOperation(operation: string): string {
    const operationMap: { [key: string]: string } = {
      'sell': 'SELL',
      'purchasing': 'PURCHASING',
      'rent in': 'RENT_IN',
      'rent out': 'RENT_OUT'
    };

    const translationKey = operationMap[operation?.toLowerCase()] || 'SELL';
    return this.translateService.instant(`HOME.PROPERTY_OPERATIONS.${translationKey}`);
  }

  // الحصول على اسم المنطقة حسب اللغة
  getAreaName(property: any): string {
    if (this.currentLang === 'en') {
      return (property.area?.name_en || property.city?.name_en || property.area?.name_ar || property.city?.name_ar || '').slice(0, 20);
    } else {
      return (property.area?.name_ar || property.city?.name_ar || property.area?.name_en || property.city?.name_en || '').slice(0, 20);
    }
  }

  // تنسيق السعر حسب اللغة
  getFormattedPrice(property: any): string {
    const price = property.totalPriceInCash || property.totalPriceInInstallment || 0;
    const currency = this.translateService.instant('HOME.CURRENCY.EGP');

    if (this.currentLang === 'ar') {
      // تحويل الأرقام إلى العربية
      const arabicPrice = this.convertToArabicNumbers(price.toLocaleString());
      return `${arabicPrice} ${currency}`;
    } else {
      return `${price.toLocaleString()} ${currency}`;
    }
  }

  // تحويل الأرقام إلى العربية
  convertToArabicNumbers(englishNumbers: string): string {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    const englishNumbersArray = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

    let result = englishNumbers;
    for (let i = 0; i < englishNumbersArray.length; i++) {
      result = result.replace(new RegExp(englishNumbersArray[i], 'g'), arabicNumbers[i]);
    }
    return result;
  }

  // تنسيق المساحة حسب اللغة
  getFormattedArea(area: number): string {
    const unit = this.translateService.instant('HOME.UNITS.SQM');

    if (this.currentLang === 'ar') {
      const arabicArea = this.convertToArabicNumbers(area.toString());
      return `${arabicArea} ${unit}`;
    } else {
      return `${area} ${unit}`;
    }
  }

  // ترجمة نوع العقار
  getTranslatedPropertyType(type: string): string {
    return this.propertyTranslationService.translatePropertyType(
      type,
      this.currentLang as 'en' | 'ar',
      20
    );
  }

  // ترجمة نوع الكمبوند
  getTranslatedCompoundType(type: string): string {
    return this.propertyTranslationService.translateCompoundType(
      type,
      this.currentLang as 'en' | 'ar',
      20
    );
  }
}

