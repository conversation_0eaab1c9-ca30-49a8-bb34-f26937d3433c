import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { UsersService, User, UsersResponse } from '../services/users.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-all-users',
  templateUrl: './all-users.component.html',
  styleUrls: ['./all-users.component.scss']
})
export class AllUsersComponent implements OnInit {

  users: any;
  loading = false;
  searchText = '';
  currentPage = 0;
  pageSize = 10;
  totalElements = 0;
  totalPages = 0;

  // Filter options
  selectedStatus = '';
  selectedUserType = '';
  selectedVerified: boolean | undefined = undefined;
  sortBy = 'id';
  sortDir = 'desc';

  constructor( protected cd: ChangeDetectorRef, private usersService: UsersService) { }

  ngOnInit(): void {
    this.loadUsers();
  }

  getStatusClass(status: boolean): string {
    switch (status) {
      case true:
        return 'badge-light-success';
      // case 'Suspended':
      //   return 'badge-light-warning';
      case false:
        return 'badge-light-danger';
      default:
        return 'badge-light-secondary';
    }
  }

  getUserTypeClass(userType: string): string {
    switch (userType) {
      case 'developer':
        return 'badge-light-primary';
      case 'broker':
        return 'badge-light-success';
      case 'client':
        return 'badge-light-info';
      case 'admin':
        return 'badge-light-danger';
      default:
        return 'badge-light-secondary';
    }
  }


  loadUsers(): void {
    this.loading = true;

    const params = {
      page: this.currentPage,
      size: this.pageSize,
      search: this.searchText || undefined,
      status: this.selectedStatus || undefined,
      userType: this.selectedUserType || undefined,
      verified: this.selectedVerified,
      sortBy: this.sortBy,
      sortDir: this.sortDir
    };

    this.usersService.getAllUsers(params).subscribe({
      next: (response) => {
        console.log(response);
        this.users = response.data;
        this.totalElements = response.count;
        this.totalPages = response.count/10;
        this.loading = false;
        this.cd.detectChanges();

      },
      error: (error) => {
        console.error('Error loading users:', error);
        this.loading = false;
        Swal.fire('Error', 'Failed to load users. Please try again.', 'error');
      }
    });
  }

 onSearchChange(searchText: string): void {
    this.searchText = searchText;
    this.currentPage = 0;
    this.loadUsers();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadUsers();
  }


  viewUser(user: User): void {
    console.log('View user:', user);
  }

}
