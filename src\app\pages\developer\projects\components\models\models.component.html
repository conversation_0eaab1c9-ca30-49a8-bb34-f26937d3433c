<div class="mb-5 mt-0">
  <app-broker-title *ngIf="user?.role === 'broker'"></app-broker-title>
</div>
<div class="card mb-5 mb-xl-10">
  <div class="card-body pt-3 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap">
      <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start flex-wrap">
          <div class="d-flex my-4">
            <h1 class="text-dark-blue fs-2 fw-bolder me-1 mt-3">Models</h1>
          </div>
          <div class="d-flex my-4">
            <form data-kt-search-element="form" class="w-300px position-relative mb-3" autocomplete="off">
              <app-keenicon name="magnifier"
                class="fs-2 fs-lg-1 text-gray-500 position-absolute top-50 translate-middle-y ms-3"
                type="outline"></app-keenicon>
              <input type="text" name="searchText"
                class="form-control form-control-flush ps-10 bg-light border rounded-pill" [(ngModel)]="searchText"
                (ngModelChange)="onSearchTextChange($event)" placeholder="Search By Code.."
                data-kt-search-element="input" />
            </form>
          </div>
          <div class="d-flex my-4">
            <div class="position-relative me-3">
              <a class="btn btn-sm btn-light-dark-blue me-3 cursor-pointer" (click)="toggleFilterDropdown()">
                <i class="fa-solid fa-filter"></i> Filter
              </a>

              <!-- Filter Dropdown -->
              <div *ngIf="isFilterDropdownVisible" class="dropdown-menu show p-3 shadow"
                style="position: absolute; top: 100%; left: 0; z-index: 1000">
                <app-model-filter (filtersApplied)="onFiltersApplied($event)"></app-model-filter>
              </div>
            </div>

            <a *ngIf="user?.role === 'developer'" class="btn btn-sm btn-success cursor-pointer">
              <i class="bi bi-file-earmark-spreadsheet me-1"></i>
              Download Excel
            </a>
          </div>
        </div>
      </div>
    </div>
    <app-empty-properties-card *ngIf="showEmptyCard" [userRole]="'developer'"></app-empty-properties-card>

    <div *ngIf="showEmptyCard && user?.role === 'developer'" class="text-center mb-5">
      <input type="file" #fileInput (change)="onFileSelected($event)" accept=".xlsx,.xls" hidden />
      <button class="btn btn-primary btn-lg mb-3" (click)="fileInput.click()">
        <i class="bi bi-file-earmark-spreadsheet me-2"></i>
        Upload Model Excel Sheet
      </button>

      <div class="text-center">
        <button class="btn btn-success btn-lg" (click)="downloadModel()">
          <i class="bi bi-download me-2"></i>
          Download Model Template
        </button>
      </div>
    </div>



    <div class="table-responsive mb-5" *ngIf="!showEmptyCard && rows.length > 0">
      <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
        <thead>
          <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
            <th class="w-25px ps-4 rounded-start">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="1" data-kt-check="true"
                  data-kt-check-target=".widget-13-check" />
              </div>
            </th>
            <th class="min-w-150px">
              Date
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-100px">
              Model Code
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-100px">
              No Of Units
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-150px">
              Units
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <!-- <th class="min-w-100px">
              Type
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th> -->
            <th class="min-w-150px">
              Area
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-100px">
              Rooms
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
            <th class="min-w-100px">
              Bathrooms
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let row of rows">
            <td class="ps-4">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input widget-13-check" type="checkbox" value="1" />
              </div>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.date }}
              </span>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.code }}
              </span>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.numberOfUnits }}
              </span>
            </td>
            <td>
              <button [routerLink]="['/developer/projects/models/units']" [queryParams]="{ modelCode: row.code }"
                class="btn btn-sm btn-light-dark-blue text-hover-dark-blue">
                {{ row.name }}
                <div class="text-muted fs-7 mt-1">Units Details</div>
              </button>
            </td>
            <!-- <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.type }}
              </span>
            </td> -->
            <td>
              <span class="fw-bold badge fs-6 fw-semibold">
                {{ row.landingArea }}
              </span>
            </td>
            <td>
              <span class="fw-bold badge fs-6 fw-semibold">
                {{ row.numberOfRooms }}
              </span>
            </td>
            <td>
              <span class="fw-bold badge fs-6 fw-semibold">
                {{ row.numberOfBathrooms }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<router-outlet></router-outlet>
