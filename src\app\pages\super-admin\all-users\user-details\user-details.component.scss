// User Details component styles
.card {
  box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
  border: 0;
  border-radius: 1rem;
}

.symbol {
  .symbol-label {
    font-size: 1rem;
    font-weight: 600;
  }
}

.badge {
  &.badge-light-success {
    background-color: rgba(34, 197, 94, 0.1);
    color: #22c55e;
  }

  &.badge-light-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }

  &.badge-light-primary {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
  }

  &.badge-light-warning {
    background-color: rgba(251, 191, 36, 0.1);
    color: #fbbf24;
  }

  &.badge-light-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
  }

  &.badge-light-secondary {
    background-color: rgba(107, 114, 128, 0.1);
    color: #6b7280;
  }
}

.btn {
  &.btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
  }
}

.text-hover-primary:hover {
  color: #3b82f6 !important;
}

.separator {
  &.separator-dashed {
    border-bottom: 1px dashed #e1e5e9;
  }
}

.rotate {
  transition: transform 0.3s ease;
  
  &.collapsed {
    transform: rotate(-90deg);
  }
}

.collapsible {
  cursor: pointer;
  
  &:hover {
    color: #3b82f6;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }
  
  .symbol-100px {
    width: 80px !important;
    height: 80px !important;
  }
  
  .fs-3 {
    font-size: 1.5rem !important;
  }
}
