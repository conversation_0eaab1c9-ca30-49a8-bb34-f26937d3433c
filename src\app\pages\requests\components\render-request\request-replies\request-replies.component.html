<div *ngIf="replies?.length > 0">
  <div class="table-responsive">
    <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
      <thead>
        <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
          <th class="w-25px ps-4 rounded-start min-w-150px cursor-pointer" (click)="sortData('broker_name')">
            Broker Name
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('broker_name') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" (click)="sortData('unit_number')">
            Unit Number
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('unit_number') }}</span>
          </th>
          <th class="min-w-100px cursor-pointer" (click)="sortData('floor')">
            Floor
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('floor') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" (click)="sortData('building_number')">
            Property Number
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('building_number') }}</span>
          </th>
          <th class="min-w-100px cursor-pointer" (click)="sortData('area')">
            Area
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('area') }}</span>
          </th>
          <th class="min-w-100px cursor-pointer" (click)="sortData('rooms_count')">
            Rooms
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('rooms_count') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" (click)="sortData('bathrooms_count')">
            Bathrooms
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('bathrooms_count') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" (click)="sortData('view')">
            View
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('view') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" (click)="sortData('delivery_date')">
            Delivery Date
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('delivery_date') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" (click)="sortData('finishing_status')">
            Finishing Status
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('finishing_status') }}</span>
          </th>
          <th class="min-w-150px">
            Unit Plan
          </th>
          <th class="min-w-250px">
            Unit Location in Master Plan
          </th>
          <th class="min-w-200px cursor-pointer" (click)="sortData('price_per_meter_cash')">
            Pricer Per Meter in Cash
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('price_per_meter_cash') }}</span>
          </th>
          <th class="min-w-250px cursor-pointer" (click)="sortData('price_per_meter_installment')">
            Pricer Per Meter in Installment
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('price_per_meter_installment') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" (click)="sortData('total_price_cash')">
            Total Price Cash
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('total_price_cash') }}</span>
          </th>
          <th class="min-w-200px cursor-pointer" (click)="sortData('total_price_installment')">
            Total Pricer Installment
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('total_price_installment') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" (click)="sortData('other_accessories')">
            Other Accessories
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('other_accessories') }}</span>
          </th>
          <th class="min-w-50px text-end rounded-end pe-4 cursor-pointer" (click)="sortData('status')">
            Status
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('status') }}</span>
          </th>
          <th class="min-w-50px text-end rounded-end pe-4 cursor-pointer">
            Chat
          </th>
          <th class="min-w-100px text-end rounded-end pe-4">Finish Request</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let reply of replies">
          <ng-container *ngFor="let unit of reply.units">
            <tr>
              <td class="ps-4">
                <span class="text-gray-900 fw-bold fs-5">{{ reply.brokerName }}</span>
              </td>
              <td>
                <span class="text-gray-900 fw-bold fs-5">{{ unit.unitNumber }}</span>
              </td>
              <td>
                <span class="text-gray-900 fw-bold fs-5">{{ unit.floor }}</span>
              </td>
              <td>
                <span class="text-gray-900 fw-bold fs-5"> {{ unit.buildingNumber }}</span>
              </td>
              <td>
                <span class="text-gray-900 fw-bold fs-5">{{ unit.unitArea | number:'1.0-2' }} m²</span>
              </td>
              <td>
                <span class="text-gray-900 fw-bold fs-5">{{ unit.numberOfRooms }}</span>
              </td>
              <td>
                <span class="text-gray-900 fw-bold fs-5">{{ unit.numberOfBathrooms }}</span>
              </td>
              <td>
                <span class="text-gray-900 fw-bold fs-5">{{ unit.view }}</span>
              </td>
              <td>
                <span class="text-gray-900 fw-bold fs-5">{{ unit.deliveryDate }}</span>
              </td>
              <td>
                <span *ngIf="unit.finishingType === 'On Brick'" class="badge badge-light-danger fs-5 p-3">
                  {{ unit.finishingType }}
                </span>
                <span *ngIf="unit.finishingType === 'Semi finished'" class="badge badge-light-danger fs-5 p-3">
                  {{ unit.finishingType }}
                  </span>
                <span *ngIf="unit.finishingType === 'Company finished'" class="badge badge-light-success fs-5 p-3">
                  {{ unit.finishingType }}
                </span>
                <span *ngIf="unit.finishingType === 'Super Lux'" class="badge badge-light-info fs-5 p-3">
                  {{ unit.finishingType }}
                </span>
                <span *ngIf="unit.finishingType === 'Ultra Super Lux'" class="badge badge-light-info fs-5 p-3">
                  {{ unit.finishingType }}
                </span>
                <span *ngIf="unit.finishingType === 'Standard'" class="badge badge-light-info fs-5">
                  {{ unit.finishingType }}
                </span>
              </td>
              <td>
                <button class="btn btn-sm btn-light-info p-3" (click)="showUnitPlanModal(unit.diagram)">
                  <i class="fa-solid fa-file-image me-1"></i> View Plan
                </button>
              </td>
              <td>
                <button class="btn btn-sm btn-light-info p-3" (click)="showUnitPlanModal(unit.locationInMasterPlan)">
                  <i class="fa-solid fa-file-image me-1"></i> View location
                </button>
              </td>
              <td>
                <span class="badge badge-light-warning fw-bold fs-5 p-3">{{ unit.pricePerMeterInCash }} EGP</span>
              </td>
              <td>
                <span class="badge badge-light-primary fw-bold fs-5 p-3">{{ unit.pricePerMeterInInstallment }} EGP</span>
              </td>
              <td>
                <span class="badge badge-light-warning fw-bold fs-5 p-3">{{ unit.totalPriceInCash }} EGP</span>
              </td>
              <td>
                <span class="badge badge-light-primary fw-bold fs-5 p-3">{{ unit.totalPriceInInstallment }} EGP</span>
              </td>
              <td>
                <span class="text-gray-900 fw-bold fs-5">{{ formatAccessories(unit.otherAccessories) }}</span>
              </td>
              <td class="text-end fs-5 pe-4">
                <span *ngIf="unit.status === 'sold'" class="badge badge-light-danger fw-bold fs-5">{{ unit.status }}</span>
                <span *ngIf="unit.status === 'available'" class="badge badge-light-warning fw-bold fs-5">{{ unit.status }}</span>
                <span *ngIf="unit.status === 'new'" class="badge badge-light-success fw-bold fs-5 p-3">{{ unit.status }}</span>
                <span *ngIf="unit.status === 'reserved'" class="badge badge-light-info fw-bold fs-5 p-3">{{ unit.status }}</span>
              </td>
              <td>
                <a
                  class="d-flex align-items-center  me-5 mb-2 mf-2"
                  [routerLink]="['/chat']"
                  [queryParams]="{ chatWithUID: reply.userId }">
                  <i class="fa-regular fa-comment-dots me-1 text-success text-hover-success fs-5"></i>
                </a>
              </td>
              <td>
                <div class="menu-item px-3" *ngIf="user?.role == 'client' && !dealClicked">
                  <a class="btn btn-sm btn-mid-blue me-3 cursor-pointer fw-bold fs-6" (click)="updateRequestStatus(requestId, user?.id, 'finished' , reply.brokerId)">
                    Deal
                  </a>
                </div>
              </td>
            </tr>
          </ng-container>
        </ng-container>
      </tbody>
    </table>
  </div>

  <div class="m-2">
    <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.limit" [currentPage]="page.pageNumber"
      (pageChange)="onPageChange($event)">
    </app-pagination>
  </div>

  <!-- Unit Plan Modal -->
  <div class="modal fade" id="viewUnitPlanModal" tabindex="-1" aria-labelledby="viewUnitPlanModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="viewUnitPlanModalLabel">Unit Plan</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body text-center">
          <img *ngIf="selectedUnitPlanImage" [src]="selectedUnitPlanImage" class="img-fluid" alt="Unit Plan">
        </div>
      </div>
    </div>
  </div>
</div>



<div *ngIf="replies?.length == 0">
  <div class="row mb-5">
    <div class="col-md-5">
      <div class="d-flex align-items-center bg-light-dark-blue rounded p-5" role="alert" aria-live="polite">
        <span class="svg-icon text-info me-5" aria-hidden="true">
            <i class="fas fa-exclamation-circle ms-1 fs-5 text-dark-blue"></i>
        </span>
        <div class="flex-grow-1 me-2">
          <span class="fw-bolder text-dark-blue fs-6">
            No Replies Available
          </span>
          <span class="text-muted fw-bold d-block">
            No replies have been received for this request yet. Please respond to the client promptly.
          </span>
        </div>
      </div>
    </div>
    <div class="col-md-7"></div>
  </div>
</div>

<div class="modal fade" id="ratingModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content p-4">
      <app-rating [brokerId]="selectedBrokerId"></app-rating>
    </div>
  </div>
</div>
