import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { RequestsComponent } from './requests.component';
import { ReceivedRequestsComponent } from './components/received-requests/received-requests.component';
import { SentRequestsComponent } from './components/sent-requests/sent-requests.component';
import { RenderRequestComponent } from './components/render-request/render-request.component';
import { RequestOverviewComponent } from './components/render-request/request-overview/request-overview.component';
import { RequestHistoryComponent } from './components/render-request/request-history/request-history.component';
import { RequestRecommendationsComponent } from './components/render-request/request-recommendations/request-recommendations.component';
import { AssignToBrokersComponent } from './components/assign-to-brokers/assign-to-brokers.component';
import { PaginationComponent } from 'src/app/pagination/pagination.component';
import { RequestRepliesComponent } from './components/render-request/request-replies/request-replies.component';

const routes: Routes = [
  {
    path: '',
    component: RequestsComponent,
    children: [
      { path: 'received', component: ReceivedRequestsComponent },
      { path: 'sent', component: SentRequestsComponent },
      { path: '', redirectTo: 'sent', pathMatch: 'full' },
    ],
  },

  {
    path: 'render/:id',
    component: RenderRequestComponent,
    children: [
      { path: '', redirectTo: 'overview', pathMatch: 'full' },
      { path: 'overview', component: RequestOverviewComponent },
      { path: 'history', component: RequestHistoryComponent },
      { path: 'replies', component: RequestRepliesComponent },
      {
        path: 'units-recommendation',
        component: RequestRecommendationsComponent,
      },
    ],
  },

  { path: 'assign-to/:id', component: AssignToBrokersComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes), PaginationComponent],
  exports: [RouterModule],
})
export class RequestsRoutingModule {}
