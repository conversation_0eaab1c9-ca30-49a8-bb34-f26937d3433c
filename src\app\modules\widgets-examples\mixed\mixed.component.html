<div class="row g-5 g-xl-8">
  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget1
      class="card card-xl-stretch mb-xl-8"
      color="primary"
    ></app-mixed-widget1>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget1
      class="card card-xl-stretch mb-xl-8"
      color="danger"
    ></app-mixed-widget1>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget1
      class="card card-xl-stretch mb-5 mb-xl-8"
      color="danger"
    ></app-mixed-widget1>
  </div>
  <!-- end::Col -->
</div>
<!-- end::Row -->

<!-- begin::Row -->
<div class="row g-5 g-xl-8">
  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget2
      class="card card-xl-stretch mb-xl-8"
      chartColor="info"
      chartHeight="200px"
      strokeColor="#4e12c4"
    ></app-mixed-widget2>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget2
      class="card card-xl-stretch mb-xl-8"
      chartColor="danger"
      chartHeight="200px"
      strokeColor="#cb1e46"
    ></app-mixed-widget2>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget2
      class="card card-xl-stretch mb-5 mb-xl-8"
      chartColor="primary"
      chartHeight="200px"
      strokeColor="#0078d0"
    ></app-mixed-widget2>
  </div>
  <!-- end::Col -->
</div>
<!-- end::Row -->

<!-- begin::Row -->
<div class="row g-5 g-xl-8">
  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget3
      class="card card-xl-stretch mb-xl-8"
      chartColor="info"
      chartHeight="250px"
    ></app-mixed-widget3>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget3
      class="card card-xl-stretch mb-xl-8"
      chartColor="danger"
      chartHeight="250px"
    ></app-mixed-widget3>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget3
      class="card card-xl-stretch mb-5 mb-xl-8"
      chartColor="primary"
      chartHeight="250px"
    ></app-mixed-widget3>
  </div>
  <!-- end::Col -->
</div>
<!-- end::Row -->

<!-- begin::Row -->
<div class="row g-5 g-xl-8">
  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget4
      class="card card-xl-stretch mb-xl-8"
      image="./assets/media/svg/brand-logos/plurk.svg"
      color="danger"
      title="Monthly Subscription"
      date="Due: 27 Apr 2020"
      progress="75%"
    ></app-mixed-widget4>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget4
      class="card card-xl-stretch mb-xl-8"
      image="./assets/media/svg/brand-logos/vimeo.svg"
      color="primary"
      title="Monthly Subscription"
      date="Due: 27 Apr 2020"
      progress="75%"
    ></app-mixed-widget4>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget4
      class="card card-xl-stretch mb-5 mb-xl-8"
      image="./assets/media/svg/brand-logos/kickstarter.svg"
      color="success"
      title="Monthly Subscription"
      date="Due: 27 Apr 2020"
      progress="75%"
    ></app-mixed-widget4>
  </div>
  <!-- end::Col -->
</div>
<!-- end::Row -->

<!-- begin::Row -->
<div class="row g-5 g-xl-8">
  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget5
      class="card card-xl-stretch mb-xl-8"
      image="./assets/media/svg/brand-logos/plurk.svg"
      time="7 hours ago"
      title="PitStop - Multiple Email Generator"
      description="Pitstop creates quick email campaigns.<br/>We help to strengthen your brand<br/>for your every purpose."
    ></app-mixed-widget5>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget5
      class="card card-xl-stretch mb-xl-8"
      image="./assets/media/svg/brand-logos/telegram.svg"
      time="10 days ago"
      title="ReactJS Admin Theme"
      description="Keenthemes uses the latest and greatest frameworks<br/>with ReactJS for complete modernization and<br/>future."
    ></app-mixed-widget5>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget5
      class="card card-xl-stretch mb-5 mb-xl-8"
      image="./assets/media/svg/brand-logos/vimeo.svg"
      time="2 weeks ago"
      title="KT.com - High Quality Templates"
      description="Easy to use, incredibly flexible and secure<br/>with in-depth documentation that outlines<br/>everything for you"
    ></app-mixed-widget5>
  </div>
  <!-- end::Col -->
</div>
<!-- end::Row -->

<!-- begin::Row -->
<div class="row g-5 g-xl-8">
  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget6
      class="card card-xl-stretch mb-xl-8"
      chartColor="primary"
      chartHeight="150px"
    ></app-mixed-widget6>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget6
      class="card card-xl-stretch mb-xl-8"
      chartColor="danger"
      chartHeight="150px"
    ></app-mixed-widget6>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget6
      class="card card-xl-stretch mb-5 mb-xl-8"
      chartColor="success"
      chartHeight="150px"
    ></app-mixed-widget6>
  </div>
  <!-- end::Col -->
</div>
<!-- end::Row -->

<!-- begin::Row -->
<div class="row g-5 g-xl-8">
  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget7
      class="card card-xl-stretch mb-xl-8"
      chartColor="primary"
      chartHeight="200px"
    ></app-mixed-widget7>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget7
      class="card card-xl-stretch mb-xl-8"
      chartColor="success"
      chartHeight="200px"
    ></app-mixed-widget7>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget7
      class="card card-xl-stretch mb-xl-8"
      chartColor="danger"
      chartHeight="200px"
    ></app-mixed-widget7>
  </div>
  <!-- end::Col -->
</div>
<!-- end::Row -->

<!-- begin::Row -->
<div class="row g-5 g-xl-8">
  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget8
      class="card card-xl-stretch mb-xl-8"
      chartColor="primary"
      chartHeight="150px"
    ></app-mixed-widget8>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget8
      class="card card-xl-stretch mb-xl-8"
      chartColor="success"
      chartHeight="150px"
    ></app-mixed-widget8>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget8
      class="card card-xl-stretch mb-5 mb-xl-8"
      chartColor="danger"
      chartHeight="150px"
    ></app-mixed-widget8>
  </div>
  <!-- end::Col -->
</div>
<!-- end::Row -->

<!-- begin::Row -->
<div class="row g-5 g-xl-8">
  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget9
      class="card card-xl-stretch mb-xl-8"
      chartColor="primary"
      chartHeight="150px"
    ></app-mixed-widget9>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget9
      class="card card-xl-stretch mb-xl-8"
      chartColor="success"
      chartHeight="150px"
    ></app-mixed-widget9>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget9
      class="card card-xl-stretch mb-xl-8"
      chartColor="danger"
      chartHeight="150px"
    ></app-mixed-widget9>
  </div>
  <!-- end::Col -->
</div>
<!-- end::Row -->

<!-- begin::Row -->
<div class="row g-5 g-xl-8">
  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget10
      class="card card-xl-stretch mb-xl-8"
      chartColor="info"
      chartHeight="150px"
    ></app-mixed-widget10>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget10
      class="card card-xl-stretch mb-xl-8"
      chartColor="warning"
      chartHeight="150px"
    ></app-mixed-widget10>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget10
      class="card card-xl-stretch mb-5 mb-xl-8"
      chartColor="primary"
      chartHeight="150px"
    ></app-mixed-widget10>
  </div>
  <!-- end::Col -->
</div>
<!-- end::Row -->

<!-- begin::Row -->
<div class="row g-5 g-xl-8">
  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget11
      class="card card-xl-stretch mb-xl-8"
      chartColor="info"
      chartHeight="200px"
    ></app-mixed-widget11>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget11
      class="card card-xl-stretch mb-xl-8"
      chartColor="warning"
      chartHeight="200px"
    ></app-mixed-widget11>
  </div>
  <!-- end::Col -->

  <!-- begin::Col -->
  <div class="col-xl-4">
    <app-mixed-widget11
      class="card card-xl-stretch mb-xl-8"
      chartColor="primary"
      chartHeight="200px"
    ></app-mixed-widget11>
  </div>
  <!-- end::Col -->
</div>
