import { ChangeDetectorRef, Component, OnInit, OnDestroy } from '@angular/core';
import { RequestService } from '../../services/request.service';
import { ActivatedRoute, ParamMap, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-render-request',
  templateUrl: './render-request.component.html',
  styleUrls: ['./render-request.component.scss'],
})
export class RenderRequestComponent implements OnInit, OnDestroy {

  userId :number;
  brokerId :number ;
  request: any = null;
  requestId: string | null = null;
  canReply: boolean = true;
  private routeSub: Subscription | null = null;
  user: any;


  constructor(
    protected cd: ChangeDetectorRef,
    protected requestService: RequestService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit() {
    const userJson = localStorage.getItem('currentUser');
    this.user = userJson ? JSON.parse(userJson) : null;
    this.userId = this.user?.id;
    this.brokerId = this.user?.brokerId;
    this.routeSub = this.route.paramMap.subscribe((params: ParamMap) => {
      this.requestId = params.get('id');
      console.log('RenderRequestComponent - Request ID:', this.requestId);
      if (this.requestId) {
        this.requestService.setRequestId(this.requestId);
        this.requestService.clearRequest(); // Clear previous request data
        this.request = null; // Reset local request
        this.cd.markForCheck();
        this.getRequest();
      } else {
        console.error('RenderRequestComponent - No request ID found in route');
        Swal.fire('Invalid request ID.', '', 'error');
      }
    });
  }

  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
  }

  getRequest() {
    if (this.requestId) {
      this.requestService.getRequestById(this.requestId).subscribe({
        next: (response: any) => {
          this.request = response.data;
          console.log('RenderRequestComponent - Request Data:', this.request);
          console.log('RenderRequestComponent - Request canReply:', this.canReply);
          this.requestService.setRequest(this.request);
          this.checkReplyAvailability();
          this.cd.markForCheck();
        },
        error: (error: any) => {
          console.error('RenderRequestComponent - Error fetching request:', error);
          this.cd.markForCheck();
          Swal.fire('Failed to load data. Please try again later.', '', 'error');
        },
      });
    }
  }

  checkReplyAvailability(): boolean {
    this.canReply = this.request?.user?.id !== this.userId;
    console.log('RenderRequestComponent - canReply:', this.canReply);
    this.cd.markForCheck();
    return this.canReply;
  }

  updateRequestStatus(requestId: number, userId: number, status: string) {
    const payload = {
      userId,
      status,
    };

    this.requestService.updateRequestStatus(requestId, payload).subscribe({
      next: (response) => {
        console.log('Status updated successfully:', response);
        this.request.status = status;
        this.cd.markForCheck();
        Swal.fire('Request status updated successfully!', '', 'success');
      },
      error: (error) => {
        console.error('Error updating status:', error);
      },
    });
  }

  archiveRequest(requestId: number, brokerId: number) {
    this.requestService.archiveRequest(requestId, brokerId).subscribe({
      next: (response) => {
        console.log('Request archived successfully:', response);
        this.cd.markForCheck();

        Swal.fire({
          title: 'Request archived successfully!',
          icon: 'success',
          confirmButtonText: 'OK'
        }).then((result) => {
          if (result.isConfirmed) {
            this.router.navigate(['/requests/received']);
          }
        });
      },
      error: (error) => {
        console.error('Error archiving request:', error);
        Swal.fire('Failed to archive request', '', 'error');
      },
    });
  }
}
