import { Component, HostBinding, Input, Output, EventEmitter } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'app-admin-card',
  templateUrl: './admin-card.component.html',
  styleUrls: ['./admin-card.component.scss']
})
export class AdminCardComponent {

  @Input() svgIcon: string = '';
  @Input() faIcon: string = '';
  @Input() title: string = '';
  @Input() description: string = '';
  @Input() backgroundColor: string = 'bg-light-primary';
  @Input() iconColor: string = 'text-primary';
  @Input() routePath: string = '';
  @Output() cardClick = new EventEmitter<string>();
  @HostBinding('class') class = 'card h-100 cursor-pointer';

  constructor(private sanitizer: DomSanitizer) {}

  get sanitizedSvgIcon(): SafeHtml {
    return this.sanitizer.bypassSecurityTrustHtml(this.svgIcon);
  }

  get isFontAwesome(): boolean {
    return !!this.faIcon;
  }

  get isSvg(): boolean {
    return !!this.svgIcon && !this.faIcon;
  }

  onCardClick(): void {
    if (this.routePath) {
      this.cardClick.emit(this.routePath);
    }
  }
}
