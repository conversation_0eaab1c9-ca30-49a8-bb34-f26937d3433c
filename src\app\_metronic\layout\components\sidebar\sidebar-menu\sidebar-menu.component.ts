import { Component, OnInit } from '@angular/core';
import { AuthenticationService } from 'src/app/pages/authentication';
import { ChatService } from 'src/app/pages/shared/services/chat.service';

@Component({
  selector: 'app-sidebar-menu',
  templateUrl: './sidebar-menu.component.html',
  styleUrls: ['./sidebar-menu.component.scss'],
})
export class SidebarMenuComponent implements OnInit {

  // user: any = { role: 'client' };
  // user: any = { role: 'broker' };
  // user: any = { role: 'developer' };

  user: any;

  messageNotificationCounter = 0;

  constructor(private chatService: ChatService,  private authenticationService: AuthenticationService) {}

  ngOnInit(): void {
    this.user = this.authenticationService.getSessionUser();
    console.log(this.user);
    this.chatService.counter$.subscribe(count => {
    this.messageNotificationCounter = count;
    });
  }
}
