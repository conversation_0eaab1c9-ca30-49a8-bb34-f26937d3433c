import { Component, HostBinding, OnInit, Output, EventEmitter, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';

@Component({
  selector: 'app-specializations-filter',
  templateUrl: './specializations-filter.component.html',
  styleUrls: ['./specializations-filter.component.scss']
})
export class SpecializationsFilterComponent implements OnInit {
  @HostBinding('class') class = 'menu menu-sub menu-sub-dropdown w-250px w-md-300px';
  @Output() filterChanged = new EventEmitter<{ specializationScope: string | null }>();
  @ViewChild('filterForm') filterForm: NgForm;

  isDropdownOpen = false; // Control dropdown visibility
  selectedSpecializationScope: string | null = null;

  specializationScope: { key: string; value: string }[] = [
    { key: 'Purchase/Sell Outside Compound', value: 'purchase_sell_outside_compound' },
    // { key: 'Purchase/Sell Inside Compound', value: 'purchase_sell_inside_compound' },
    { key: 'Primary Inside Compound', value: 'primary_inside_compound' },
    { key: 'Resale Inside Compound', value: 'resale_inside_compound' },
    { key: 'Rentals Outside Compound', value: 'rentals_outside_compound' },
    { key: 'Rentals Inside Compound', value: 'rentals_inside_compound' }
  ];

  constructor() {}

  ngOnInit(): void {}

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
    console.log('Dropdown toggled:', this.isDropdownOpen);
  }

  onFilter(): void {
    const payload = {
      specializationScope: this.selectedSpecializationScope
    };
    this.filterChanged.emit(payload);
    this.isDropdownOpen = false; // Close dropdown on apply
  }

  onReset(): void {
    this.selectedSpecializationScope = null;
    this.filterForm.resetForm();
    this.filterChanged.emit({ specializationScope: null });
    this.isDropdownOpen = false; // Close dropdown on reset
  }
}
