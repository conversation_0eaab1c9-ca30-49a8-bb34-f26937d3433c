import { Component, OnInit } from '@angular/core';
import { IconUserModel } from '../../../_metronic/partials';

@Component({
  selector: 'app-projects',
  templateUrl: './projects.component.html',
})
export class ProjectsComponent implements OnInit {
  users1: Array<IconUserModel> = [
    { name: '<PERSON>', avatar: './assets/media/avatars/300-6.jpg' },
    { name: '<PERSON>', avatar: './assets/media/avatars/300-1.jpg' },
    { name: '<PERSON>', initials: 'S', color: 'primary' },
  ];

  users2 = [
    { name: '<PERSON>', initials: 'A', color: 'warning' },
    { name: '<PERSON>', avatar: './assets/media/avatars/300-5.jpg' },
  ];

  users3 = [
    { name: '<PERSON> <PERSON><PERSON>', avatar: './assets/media/avatars/300-6.jpg' },
    { name: '<PERSON><PERSON>', avatar: './assets/media/avatars/300-1.jpg' },
    { name: '<PERSON>', initials: '<PERSON>', color: 'info' },
  ];

  users4 = [
    { name: '<PERSON><PERSON>', initials: '<PERSON>', color: 'warning' },
    { name: 'Rob <PERSON>', initials: 'R', color: 'success' },
  ];

  users5 = [
    { name: '<PERSON> <PERSON>am', avatar: './assets/media/avatars/300-20.jpg' },
    { name: '<PERSON> <PERSON>ston', avatar: './assets/media/avatars/300-7.jpg' },
    { name: 'Susan Redwood', initials: 'S', color: 'primary' },
  ];

  users6 = [
    { name: 'Emma Smith', avatar: './assets/media/avatars/300-6.jpg' },
    { name: 'Rudy Stone', avatar: './assets/media/avatars/300-1.jpg' },
    { name: 'Susan Redwood', initials: 'S', color: 'primary' },
  ];

  users7 = [
    { name: 'Meloday Macy', avatar: './assets/media/avatars/300-2.jpg' },
    { name: 'Rabbin Watterman', initials: 'S', color: 'success' },
  ];

  users8 = [
    { name: 'Emma Smith', avatar: './assets/media/avatars/300-6.jpg' },
    { name: 'Rudy Stone', avatar: './assets/media/avatars/300-1.jpg' },
    { name: 'Susan Redwood', initials: 'S', color: 'primary' },
  ];

  users9 = [
    { name: 'Meloday Macy', avatar: './assets/media/avatars/300-2.jpg' },
    { name: 'Rabbin Watterman', initials: 'S', color: 'danger' },
  ];

  constructor() {}

  ngOnInit(): void {}
}
