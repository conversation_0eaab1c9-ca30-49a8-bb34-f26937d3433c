import { Injectable } from '@angular/core';
import { Validators } from '@angular/forms';
import { BaseConfigService, InputConfig, StepperConfiguration } from '../base-config.service';
import {
  ACTIVITY_TYPES_OPTIONS,
  FIT_OUT_CONDITION_TYPES_OPTIONS,
  FLOOR_TYPES_OPTIONS,
  FURNISHING_STATUS_OPTIONS,
  OTHER_ACCESSORIES_OPTIONS,
  PAYMENT_METHOD_OPTIONS,
  PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
  PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
  Purchase_OTHER_ACCESSORIES_OPTIONS,
  PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
  PURCHASE_VACATION_UNIT_VIEW_TYPES_OPTIONS,
  SPECIFIC_FINISHING_STATUS_TYPES_OPTIONS,
  SUB_UNIT_OPTIONS,
} from '../../stepper-modal.constants';

@Injectable({
  providedIn: 'root'
})
export class PurchasingConfigService extends BaseConfigService {

  /**
   * Create enhanced financial inputs with payment methods for purchasing
   */
  private createPurchasingFinancialInputs(stepperModal: any): InputConfig[] {
    return this.createFinancialInputs(stepperModal);
  }

   protected createPurchaseLocationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 2,
        name: 'compoundName',
        type: 'text',
        label: 'Compound Name',
        validators: [],
        visibility: () => stepperModal.getInsideCompoundPrivilege(),
      },
      {
        step: 2,
        name: 'locationSuggestions',
        type: 'checkbox',
        label: 'Location Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 2,
        name: 'cityId',
        type: 'select',
        label: 'City',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'areaId',
        type: 'select',
        label: 'Area',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'subAreaId',
        type: 'select',
        label: 'Sub Area',
        options: [],
        validators: [],
        visibility: () => true,
      },
    ];
  }

   protected createCommercialUnitInformationInputs(stepperModal: any, includeFitOut: boolean = false): InputConfig[] {
      const baseInputs: InputConfig[] = [
        {
          step: 3,
          name: 'unitAreaMin',
          type: 'number',
          label: 'Unit Area Min (m²)',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'unitAreaMax',
          type: 'number',
          label: 'Unit Area Max (m²)',
          validators: [Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'areaSuggestions',
          type: 'checkbox',
          label: 'Area Suggestions',
          validators: [],
          visibility: () => stepperModal.isClient(),
        },
        {
          step: 3,
          name: 'floor',
          type: 'select',
          label: 'Floor',
          options: FLOOR_TYPES_OPTIONS,
          validators: [Validators.required],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'unitView',
          type: 'select',
          label: 'View',
          options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
          validators: [],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'finishingStatus',
          type: 'select',
          label: 'Finishing Status',
          options: SPECIFIC_FINISHING_STATUS_TYPES_OPTIONS,
          validators: [Validators.required],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'deliveryStatus',
          type: 'select',
          label: 'Delivery Status',
          options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
          validators: [Validators.required],
          visibility: () => true,
        },
      ];

      console.log('incude fit',includeFitOut);

      // Add fitout condition for administrative units
      if (includeFitOut == true) {
        baseInputs.push({
          step: 3,
          name: 'fitOutCondition',
          type: 'select',
          label: 'Fitout Condition',
          options: FIT_OUT_CONDITION_TYPES_OPTIONS,
          validators: [Validators.required],
          visibility: () => true,
        });
      }

      // Add common ending inputs
      baseInputs.push(
        {
          step: 3,
          name: 'otherAccessories',
          type: 'multiSelect',
          label: 'Other Accessories',
          options: Purchase_OTHER_ACCESSORIES_OPTIONS,
          validators: [],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'notes',
          type: 'textarea',
          label: 'Notes',
          validators: [],
          visibility: () => true,
        }
      );

      return baseInputs;
    }

    /**
     * Helper function to create shop unit information inputs for step 3
     */
    protected createShopUnitInformationInputs(stepperModal: any): InputConfig[] {
      return [
        {
          step: 3,
          name: 'unitAreaMin',
          type: 'number',
          label: 'Unit Area Min (m²)',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'unitAreaMax',
          type: 'number',
          label: 'Unit Area Max (m²)',
          validators: [Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'areaSuggestions',
          type: 'checkbox',
          label: 'Area Suggestions',
          validators: [],
          visibility: () => stepperModal.isClient(),
        },
        {
          step: 3,
          name: 'floor',
          type: 'select',
          label: 'Floor',
          options: FLOOR_TYPES_OPTIONS,
          validators: [Validators.required],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'unitView',
          type: 'select',
          label: 'View',
          options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
          validators: [],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'finishingStatus',
          type: 'select',
          label: 'Finishing Status',
          options: SPECIFIC_FINISHING_STATUS_TYPES_OPTIONS,
          validators: [Validators.required],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'deliveryStatus',
          type: 'select',
          label: 'Delivery Status',
          options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
          validators: [Validators.required],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'shopActivity',
          type: 'text',
          label: 'Activity',
          validators: [],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'otherAccessories',
          type: 'multiSelect',
          label: 'Other Accessories',
          options: Purchase_OTHER_ACCESSORIES_OPTIONS,
          validators: [],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'notes',
          type: 'textarea',
          label: 'Notes',
          validators: [],
          visibility: () => true,
        },
      ];
    }

    /**
     * Helper function to create commercial administrative building unit information inputs for step 3
     */
    protected createCommercialAdminBuildingUnitInformationInputs(stepperModal: any): InputConfig[] {
      return [
        {
          step: 3,
          name: 'groundAreaMin',
          type: 'number',
          label: 'Ground Area Min (m²)',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'groundAreaMax',
          type: 'number',
          label: 'Ground Area Max (m²)',
          validators: [Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'areaSuggestions',
          type: 'checkbox',
          label: 'Area Suggestions',
          validators: [],
          visibility: () => stepperModal.isClient(),
        },
        {
          step: 3,
          name: 'buildingAreaMin',
          type: 'number',
          label: 'Building Area Min (m²)',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'buildingAreaMax',
          type: 'number',
          label: 'Building Area Max (m²)',
          validators: [Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'buildingAreaSuggestions',
          type: 'checkbox',
          label: 'Building Area Suggestions',
          validators: [],
          visibility: () => stepperModal.isClient(),
        },
        {
          step: 3,
          name: 'numberOfFloors',
          type: 'number',
          label: 'Number of Floors',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'unitView',
          type: 'select',
          label: 'View',
          options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
          validators: [],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'finishingStatus',
          type: 'select',
          label: 'Finishing Status',
          options: SPECIFIC_FINISHING_STATUS_TYPES_OPTIONS,
          validators: [Validators.required],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'deliveryStatus',
          type: 'select',
          label: 'Delivery Status',
          options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
          validators: [Validators.required],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'activity',
          type: 'select',
          label: 'Activity Status',
          options: ACTIVITY_TYPES_OPTIONS,
          validators: [Validators.required],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'otherAccessories',
          type: 'multiSelect',
          label: 'Other Accessories',
          options: Purchase_OTHER_ACCESSORIES_OPTIONS,
          validators: [],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'notes',
          type: 'textarea',
          label: 'Notes',
          validators: [],
          visibility: () => true,
        },
      ];
    }

    protected createVacationVillasInformationInputs(stepperModal: any): InputConfig[] {
      return [
           {
             step: 3,
             name: 'villageName',
             type: 'text',
             label: 'Village Name',
             validators: [Validators.required],
             visibility: () => true,
           },
            {
             step: 3,
             name: 'subUnitType',
             type: 'select',
             label: 'SubUnit Type',
             options: SUB_UNIT_OPTIONS,
             validators: [Validators.required],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'unitAreaMin',
             type: 'number',
             label: 'Unit Area Min (m²)',
             validators: [Validators.required, Validators.min(0)],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'unitAreaMax',
             type: 'number',
             label: 'Unit Area Max (m²)',
             validators: [Validators.required, Validators.min(0)],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'areaSuggestions',
             type: 'checkbox',
             label: 'Area Suggestions',
             validators: [],
             visibility: () => stepperModal.isClient(),
           },
          {
             step: 3,
             name: 'numberOfFloors',
             type: 'number',
             label: 'Number of Floors',
             validators: [Validators.required, Validators.min(0)],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'unitView',
             type: 'select',
             label: 'View',
             options: PURCHASE_VACATION_UNIT_VIEW_TYPES_OPTIONS,
             validators: [],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'rooms',
             type: 'number',
             label: 'Number of Rooms',
             validators: [Validators.min(0)],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'bathRooms',
             type: 'number',
             label: 'Number of Bathrooms',
             validators: [Validators.min(0)],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'finishingStatus',
             type: 'select',
             label: 'Finishing Status',
             options: PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
             validators: [Validators.required],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'deliveryStatus',
             type: 'select',
             label: 'Delivery Status',
             options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
             validators: [Validators.required],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'furnishingStatus',
             type: 'select',
             label: 'Furnishing Status',
             options: FURNISHING_STATUS_OPTIONS,
             validators: [Validators.required],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'notes',
             type: 'textarea',
             label: 'Notes',
             validators: [],
             visibility: () => true,
           },
         ];
    }

    protected createChaletsInformationInputs(stepperModal: any): InputConfig[] {
      return [
           {
             step: 3,
             name: 'villageName',
             type: 'text',
             label: 'Village Name',
             validators: [Validators.required],
             visibility: () => true,
           },
            {
             step: 3,
             name: 'subUnitType',
             type: 'select',
             label: 'SubUnit Type',
             options: SUB_UNIT_OPTIONS,
             validators: [Validators.required],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'unitAreaMin',
             type: 'number',
             label: 'Unit Area Min (m²)',
             validators: [Validators.required, Validators.min(0)],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'unitAreaMax',
             type: 'number',
             label: 'Unit Area Max (m²)',
             validators: [Validators.required, Validators.min(0)],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'areaSuggestions',
             type: 'checkbox',
             label: 'Area Suggestions',
             validators: [],
             visibility: () => stepperModal.isClient(),
           },
           {
             step: 3,
             name: 'floor',
             type: 'select',
             label: 'Floor',
             options: FLOOR_TYPES_OPTIONS,
             validators: [Validators.required],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'unitView',
             type: 'select',
             label: 'View',
             options: PURCHASE_VACATION_UNIT_VIEW_TYPES_OPTIONS,
             validators: [],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'rooms',
             type: 'number',
             label: 'Number of Rooms',
             validators: [Validators.min(0)],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'bathRooms',
             type: 'number',
             label: 'Number of Bathrooms',
             validators: [Validators.min(0)],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'finishingStatus',
             type: 'select',
             label: 'Finishing Status',
             options: PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
             validators: [Validators.required],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'deliveryStatus',
             type: 'select',
             label: 'Delivery Status',
             options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
             validators: [Validators.required],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'furnishingStatus',
             type: 'select',
             label: 'Furnishing Status',
             options: FURNISHING_STATUS_OPTIONS,
             validators: [Validators.required],
             visibility: () => true,
           },
           {
             step: 3,
             name: 'notes',
             type: 'textarea',
             label: 'Notes',
             validators: [],
             visibility: () => true,
           },
         ];
    }

  /**
   * Configuration builder for primary inside compound purchasing units
   */
  private createPrimaryInsideCompoundPurchasingConfig(
    unitType: string,
    stepperModal: any,
    options: { includeRooms?: boolean; includeDocuments?: boolean } = {}
  ): InputConfig[] {
    const { includeRooms = true, includeDocuments = false } = options;
    const config: InputConfig[] = [
      ...this.createPurchaseLocationInputs(stepperModal),
      ...this.createUnitInformationInputs(stepperModal, includeRooms),
    ];

    // Add documents if needed
    if (includeDocuments) {
      config.push(...this.createDocumentInputs());
    }

    // Add enhanced financial inputs with payment methods
    config.push(...this.createPurchasingFinancialInputs(stepperModal));
    return config;
  }

  protected createPurchaseHouseUnitInformationInputs(stepperModal: any): InputConfig[] {
      return [
        {
          step: 3,
          name: 'groundAreaMin',
          type: 'number',
          label: 'Ground Area Min (m²)',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'groundAreaMax',
          type: 'number',
          label: 'Ground Area Max (m²)',
          validators: [Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'areaSuggestions',
          type: 'checkbox',
          label: 'Area Suggestions',
          validators: [],
          visibility: () => stepperModal.isClient(),
        },
        {
          step: 3,
          name: 'buildingAreaMin',
          type: 'number',
          label: 'Building Area Min (m²)',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'buildingAreaMax',
          type: 'number',
          label: 'Building Area Max (m²)',
          validators: [Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'buildingAreaSuggestions',
          type: 'checkbox',
          label: 'Building Area Suggestions',
          validators: [],
          visibility: () => stepperModal.isClient(),
        },
        {
          step: 3,
          name: 'numberOfFloors',
          type: 'number',
          label: 'Number of Floors',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'rooms',
          type: 'number',
          label: 'Number of Rooms',
          validators: [Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'bathRooms',
          type: 'number',
          label: 'Number of Bathrooms',
          validators: [Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'unitView',
          type: 'select',
          label: 'View',
          options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
          validators: [],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'finishingStatus',
          type: 'select',
          label: 'Finishing Status',
          options: PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
          validators: [Validators.required],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'deliveryStatus',
          type: 'select',
          label: 'Delivery Status',
          options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
          validators: [Validators.required],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'otherAccessories',
          type: 'multiSelect',
          label: 'Other Accessories',
          options: OTHER_ACCESSORIES_OPTIONS,
          validators: [],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'notes',
          type: 'textarea',
          label: 'Notes',
          validators: [],
          visibility: () => true,
        },
      ];
    }

  /**
   * Configuration builder for primary inside compound purchasing penthouses
   */
  private createPrimaryInsideCompoundPurchasingPenthousesConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createPenthouseUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary inside compound purchasing villas
   */
  private createPrimaryInsideCompoundPurchasingVillasConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createVillaUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary inside compound purchasing twin houses
   */
  private createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createPurchaseHouseUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary inside compound purchasing town houses
   */
  private createPrimaryInsideCompoundPurchasingTownHousesConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createPurchaseHouseUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary inside compound purchasing standalone villas
   */
  private createPrimaryInsideCompoundPurchasingStandaloneVillasConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createPurchaseHouseUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary inside compound purchasing administrative units
   */
  private createPrimaryInsideCompoundPurchasingAdministrativeUnitsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createCommercialUnitInformationInputs(stepperModal, false), // Include fitout condition
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary inside compound purchasing medical clinics
   */
  private createPrimaryInsideCompoundPurchasingMedicalClinicsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createCommercialUnitInformationInputs(stepperModal, false), // No fitout condition
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary inside compound purchasing pharmacies
   */
  private createPrimaryInsideCompoundPurchasingPharmaciesConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createCommercialUnitInformationInputs(stepperModal, false), // No fitout condition
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary inside compound purchasing shops
   */
  private createPrimaryInsideCompoundPurchasingShopsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createShopUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary inside compound purchasing commercial administrative buildings
   */
  private createPrimaryInsideCompoundPurchasingCommercialAdministrativeBuildingsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createCommercialAdminBuildingUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createPrimaryInsideCompoundPurchasingVacationVillasConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createVacationVillasInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createPrimaryInsideCompoundPurchasingChaletsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createChaletsInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  // RESALE INSIDE COMPOUND PURCHASING CONFIGURATIONS
  private createResaleInsideCompoundPurchasingApartmentsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createUnitInformationInputs(stepperModal, true), // Include rooms
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingDuplexesConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createUnitInformationInputs(stepperModal, true), // Include rooms
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingStudiosConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createUnitInformationInputs(stepperModal, false), // No rooms for studios
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingPenthousesConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createPenthouseUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingVillasConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createVillaUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingTwinHousesConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createPurchaseHouseUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingTownHousesConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createPurchaseHouseUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingStandaloneVillasConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createPurchaseHouseUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingAdministrativeUnitsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createCommercialUnitInformationInputs(stepperModal, false), // Include fitout condition
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingMedicalClinicsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createCommercialUnitInformationInputs(stepperModal, false), // No fitout condition
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingPharmaciesConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createCommercialUnitInformationInputs(stepperModal, false), // No fitout condition
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingShopsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createShopUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingCommercialAdministrativeBuildingsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createCommercialAdminBuildingUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingVacationVillasConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createVacationVillasInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingChaletsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createChaletsInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  getInputConfigs(stepperModal: any): StepperConfiguration[] {
    return [
      // PRIMARY INSIDE COMPOUND PURCHASING
      {
        key: 'primary_inside_compound_purchasing_apartments',
        value: this.createPrimaryInsideCompoundPurchasingConfig('apartments', stepperModal, {
          includeRooms: true,
          includeDocuments: false
        }),
      },
      {
        key: 'primary_inside_compound_purchasing_duplexes',
        value: this.createPrimaryInsideCompoundPurchasingConfig('duplexes', stepperModal, {
          includeRooms: true,
          includeDocuments: false
        }),
      },
      {
        key: 'primary_inside_compound_purchasing_studios',
        value: this.createPrimaryInsideCompoundPurchasingConfig('studios', stepperModal, {
          includeRooms: false,
          includeDocuments: false
        }),
      },
      {
        key: 'primary_inside_compound_purchasing_penthouses',
        value: this.createPrimaryInsideCompoundPurchasingPenthousesConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_i_villa',
        value: this.createPrimaryInsideCompoundPurchasingVillasConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_twin_houses',
        value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_town_houses',
        value: this.createPrimaryInsideCompoundPurchasingTownHousesConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_standalone_villas',
        value: this.createPrimaryInsideCompoundPurchasingStandaloneVillasConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_administrative_units',
        value: this.createPrimaryInsideCompoundPurchasingAdministrativeUnitsConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_medical_clinics',
        value: this.createPrimaryInsideCompoundPurchasingMedicalClinicsConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_pharmacies',
        value: this.createPrimaryInsideCompoundPurchasingPharmaciesConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_shops',
        value: this.createPrimaryInsideCompoundPurchasingShopsConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_commercial_administrative_buildings',
        value: this.createPrimaryInsideCompoundPurchasingCommercialAdministrativeBuildingsConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_vacation_villa',
        value: this.createPrimaryInsideCompoundPurchasingVacationVillasConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_chalets',
        value: this.createPrimaryInsideCompoundPurchasingChaletsConfig(stepperModal),
      },

      // RESALE INSIDE COMPOUND PURCHASING
      {
        key: 'resale_inside_compound_purchasing_apartments',
        value: this.createResaleInsideCompoundPurchasingApartmentsConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_duplexes',
        value: this.createResaleInsideCompoundPurchasingDuplexesConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_studios',
        value: this.createResaleInsideCompoundPurchasingStudiosConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_penthouses',
        value: this.createResaleInsideCompoundPurchasingPenthousesConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_i_villa',
        value: this.createResaleInsideCompoundPurchasingVillasConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_twin_houses',
        value: this.createResaleInsideCompoundPurchasingTwinHousesConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_town_houses',
        value: this.createResaleInsideCompoundPurchasingTownHousesConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_standalone_villas',
        value: this.createResaleInsideCompoundPurchasingStandaloneVillasConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_administrative_units',
        value: this.createResaleInsideCompoundPurchasingAdministrativeUnitsConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_medical_clinics',
        value: this.createResaleInsideCompoundPurchasingMedicalClinicsConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_pharmacies',
        value: this.createResaleInsideCompoundPurchasingPharmaciesConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_shops',
        value: this.createResaleInsideCompoundPurchasingShopsConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_commercial_administrative_buildings',
        value: this.createResaleInsideCompoundPurchasingCommercialAdministrativeBuildingsConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_vacation_villa',
        value: this.createResaleInsideCompoundPurchasingVacationVillasConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_chalets',
        value: this.createResaleInsideCompoundPurchasingChaletsConfig(stepperModal),
      },
    ];
  }

  getConfigurationKeys(): string[] {
    return [
      // PRIMARY INSIDE COMPOUND PURCHASING
      'primary_inside_compound_purchasing_apartments',
      'primary_inside_compound_purchasing_duplexes',
      'primary_inside_compound_purchasing_studios',
      'primary_inside_compound_purchasing_penthouses',
      'primary_inside_compound_purchasing_i_villa',
      'primary_inside_compound_purchasing_twin_houses',
      'primary_inside_compound_purchasing_town_houses',
      'primary_inside_compound_purchasing_standalone_villas',
      'primary_inside_compound_purchasing_administrative_units',
      'primary_inside_compound_purchasing_medical_clinics',
      'primary_inside_compound_purchasing_pharmacies',
      'primary_inside_compound_purchasing_shops',
      'primary_inside_compound_purchasing_commercial_administrative_buildings',
      'primary_inside_compound_purchasing_vacation_villa',
      'primary_inside_compound_purchasing_chalets',
      // RESALE INSIDE COMPOUND PURCHASING
      'resale_inside_compound_purchasing_apartments',
      'resale_inside_compound_purchasing_duplexes',
      'resale_inside_compound_purchasing_studios',
      'resale_inside_compound_purchasing_penthouses',
      'resale_inside_compound_purchasing_i_villa',
      'resale_inside_compound_purchasing_twin_houses',
      'resale_inside_compound_purchasing_town_houses',
      'resale_inside_compound_purchasing_standalone_villas',
      'resale_inside_compound_purchasing_administrative_units',
      'resale_inside_compound_purchasing_medical_clinics',
      'resale_inside_compound_purchasing_pharmacies',
      'resale_inside_compound_purchasing_shops',
      'resale_inside_compound_purchasing_commercial_administrative_buildings',
      'resale_inside_compound_purchasing_vacation_villa',
      'resale_inside_compound_purchasing_chalets'
    ];
  }
}
