import { Injectable } from "@angular/core";
import { AbstractCrudService } from "../../shared/services/abstract-crud.service";
import { environment } from "src/environments/environment";
import { Observable } from "rxjs";
import { HttpParams } from "@angular/common/http";

@Injectable({
    providedIn: 'root',
})

export class BrokerService extends AbstractCrudService {

  apiUrl = `${environment.apiUrl}/users`;

  getImages(brokerId: any): Observable<any> {
    let url =  `${environment.apiUrl}/broker/maps`;
      const params = new HttpParams()
      .set('limit', 50)
      .set('offset', 0)
      .set('sort', 'DESC')
      .set('sortBy', 'id')
      .set('brokerId', brokerId);

    return this.http.get<any[]>(url, { params });
  }

  uploadImage(brokerId : any , model: any): Observable<any> {
    let url =  `${environment.apiUrl}/broker/${brokerId}/create-map`;
    return this.http.post(url, model);
  }

  getLatestRequests(userId: number): Observable<Request[]> {
    const url = `${environment.apiUrl}/requests/latest/${userId}`;
    return this.http.get<Request[]>(url);
  }

  getBrokerRequestStatistics(userId: number, specializationScope?: string | null): Observable<any> {

    let url = `${environment.apiUrl}/requests/statistics/${userId}`;
    if (specializationScope) {
      url += `/${encodeURIComponent(specializationScope)}`;
    }
    return this.http.get<any>(url);
  }

  getBrokerDashboardStatistics(brokerId: number): Observable<Request[]> {
    const url = `${environment.apiUrl}/broker/dashboard-statistics/${brokerId}`;
    return this.http.get<Request[]>(url);
  }

  rateBroker(userId: number, payload: { ratedId: number; rate: number }): Observable<any> {
    return this.http.put(`${this.apiUrl}/${userId}/rate`, payload);
  }
}
