<div class="card-body p-10 d-flex flex-column flex-center text-center" (click)="onCardClick()">
  <!-- Icon Section -->
  <div class="mb-7">
    <div class="symbol symbol-circle symbol-100px">
      <div class="symbol-label" [ngClass]="[backgroundColor, iconColor]">
        <!-- FontAwesome Icon -->
        <i *ngIf="isFontAwesome" [class]="faIcon + ' fs-3x'"></i>
        <!-- SVG Icon -->
        <div *ngIf="isSvg" class="svg-icon svg-icon-3x" [innerHTML]="sanitizedSvgIcon"></div>
      </div>
    </div>
  </div>

  <!-- Content Section -->
  <div>
    <!-- Title using Metronic typography -->
    <h3 class="fs-2 fw-bold text-gray-900 mb-4">{{ title }}</h3>

    <!-- Description using Metronic typography -->
    <p class="fs-6 fw-semibold text-gray-600 mb-0">{{ description }}</p>
  </div>
</div>
