import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Page } from '../../../models/page.model';
import Swal from 'sweetalert2';

@Component({
    selector: 'app-base-add-update-action-menu-item',
    templateUrl: './base-add-update-action-menu-item.component.html',
    styleUrls: ['./base-add-update-action-menu-item.component.scss'],
})
export class BaseAddUpdateActionMenuItemComponent implements OnInit {
    @Input() id: number | any;
   
    @Output() reloadMethod: EventEmitter<any> = new EventEmitter<string>();
    @Input() page: Page;

    form: FormGroup;
    model: any;
    actionName: string;
    actionLabel: string;
    actionIcon: string;
    _service: any;
    modalRefElem: string;
    modalScreenSize: string = 'xl';

    constructor(protected modalService: NgbModal) {}

    setService(_service: any) {
        this._service = _service;
    }

    setModel(_model: any) {
        this.model = _model;
    }

    ngOnInit() {
        this.model = new this.model();
        if (this.id) {
            this.actionName = 'update';
            this.actionLabel = 'Update';
            this.actionIcon = 'pen-to-square';
        } else {
            this.actionName = 'add';
            this.actionLabel = 'Add';
            this.actionIcon = 'plus';
        }
    }

    async showModal(content: any, screenSize = 'l') {
        this.modalRefElem = content;
        this.modalScreenSize = screenSize;
        if (this.id) {
            this.loadDataToEdit();
        }
        this.modalService.open(content, { size: screenSize, ariaLabelledBy: 'modal-basic-title' }).result.then(
            (result) => {
                //save form
                if (this.actionName === 'add') {
                    this.create();
                } else {
                    this.update();
                }
                //reload the grid
                setTimeout(() => {
                    this.reloadMethod.emit(this.page);
                }, 500);
            },
            (reason) => {}
        );
    }

    async create() {
        console.log(this.form.value);
        await this._service.create(this.form.value).subscribe({
            next: (response: any) => {
                Swal.fire(response.message, '', 'success');
                this.form.reset({});
                this.postUpdateActions();
            },
            error: (err: any) => {
                Swal.fire(err.error.message, '', 'error');
                this.showModal(this.modalRefElem, this.modalScreenSize);
            },
        });
    }

    update() {
        console.log(this.form.value);
        this._service.update(this.id, this.form.value).subscribe({
            next: (response: any) => {
                console.log(response);
                Swal.fire(response.message, '', 'success');
                this.postUpdateActions();
                // this.reloadMethod.emit(this.page);
            },
            error: (err: any) => {
                Swal.fire(err.error.message, '', 'error');
                this.showModal(this.modalRefElem, this.modalScreenSize);
            },
        });
    }

    loadDataToEdit() {
        console.log(this.form);
        this._service.getById(this.id).subscribe({
            next: (response: any) => {
                let data = response.data;
                console.log(data);
                this.form.setValue(this.model.fillDataForEdit(data));
            },
            error: (err: any) => {
                Swal.fire(err.error.message, '', 'error');
            },
        });
    }

    postUpdateActions() {}
}
