import { Injectable } from '@angular/core';
import { AbstractCrudService } from '../../services/abstract-crud.service';
import { environment } from 'src/environments/environment';
// import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ProfileService extends AbstractCrudService {
  apiUrl = `${environment.apiUrl}/users`;

  getCurrentUserProfile(): Observable<any> {
    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;
    return this.getById(user?.id);
  }

  //******************************************************* */
  updateProfile(id: any, userData: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/update-profile/${id}`, userData);

  }

  updateProfileImage(userId: number, formData: FormData): Observable<any> {
    console.log(formData);
    return this.http.post(`${this.apiUrl}/update-image/${userId}`, formData);
  }

  //******************************************** */

  //update specializations-areas

  updateAdvertisements(id: number, model: any): Observable<any> {
    return this.http.put(
      `${this.apiUrl}/update-specializations-areas/${id}`,
      model
    );
  }
}
