<div class="card card-flush" [ngClass]="cssClass">
  <div class="card-header pt-5">
    <div class="card-title d-flex flex-column">
      <div class="d-flex align-items-center">
        <span class="fs-1 fw-bold text-gray-900 me-2 lh-1 ls-n2">Contract Requests</span>
      </div>
      <span class="text-gray-500 pt-1 fw-semibold fs-6">Analysis of contract requests</span>
    </div>
  </div>
  <div class="card-body pt-2 pb-4 d-flex flex-wrap align-items-center">
    <div class="d-flex flex-center me-5 pt-2">
      <div id="kt_card_widget_17_chart_cus_super_admin"
          [ngStyle]="{'min-width': chartSize + 'px', 'min-height': chartSize + 'px'}"
          [attr.data-kt-size]="chartSize"
          [attr.data-kt-line]="chartLine">
      </div>
    </div>
    <div class="d-flex flex-column content-justify-center flex-row-fluid">
      <div class="d-flex fw-semibold align-items-center">
        <div class="bullet w-8px h-3px rounded-2 bg-warning me-3"></div>
        <div class="text-gray-500 flex-grow-1 me-4">Pending</div>
        <div class="fw-bolder text-gray-700 text-xxl-end">{{ pending }}</div>
      </div>
      <div class="d-flex fw-semibold align-items-center">
        <div class="bullet w-8px h-3px rounded-2 bg-success me-3"></div>
        <div class="text-gray-500 flex-grow-1 me-4">Accepted</div>
        <div class="fw-bolder text-gray-700 text-xxl-end">{{ accepted }}</div>
      </div>
      <div class="d-flex fw-semibold align-items-center">
        <div class="bullet w-8px h-3px rounded-2 bg-danger me-3"></div>
        <div class="text-gray-500 flex-grow-1 me-4">Declined</div>
        <div class="fw-bolder text-gray-700 text-xxl-end">{{ declined }}</div>
      </div>
    </div>
  </div>
</div>
