// All Properties Component Styles

/* Clean Header Styles */
.simple-header {
  padding: 1.25rem 1.5rem;
  margin: 1rem -15px 1.5rem -15px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 3px solid #28a745;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  border-radius: 12px;

  h1 {
    font-weight: 600;
    color: #28a745;
    margin-bottom: 0.25rem;
    font-size: 1.75rem;
  }

  p {
    color: #6c757d;
    margin-bottom: 0;
    font-size: 0.95rem;
  }

  .btn {
    border-radius: 8px;
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    transition: all 0.2s ease;
    background: #28a745;
    border: none;
    color: white;
    font-size: 0.9rem;

    &:hover {
      background: #218838;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(40,167,69,0.3);
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-header {
    padding: 1rem 1.25rem;

    .d-flex {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 0.75rem;
    }

    h1 {
      font-size: 1.5rem;
    }

    p {
      font-size: 0.9rem;
    }

    .btn {
      align-self: flex-start;
      padding: 0.45rem 1rem;
      font-size: 0.85rem;
    }
  }
}

/* Green Theme Colors */
.property-card {
  .property-badge .badge {
    background-color: #28a745 !important;
    color: white;
  }

  .property-price h5 {
    color: #28a745 !important;
  }

  .btn-outline-primary {
    border-color: #28a745;
    color: #28a745;

    &:hover {
      background-color: #28a745;
      border-color: #28a745;
      color: white;
    }

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
  }
}

/* Green Spinner */
.spinner-border.text-primary {
  color: #28a745 !important;
}

/* Green Load More Button */
.btn-primary {
  background-color: #28a745;
  border-color: #28a745;

  &:hover {
    background-color: #218838;
    border-color: #1e7e34;
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
  }
}

/* إصلاح Property Cards في العربية */
:host-context(html[dir="rtl"]) .property-badge,
:host-context(html[lang="ar"]) .property-badge,
:host-context(.rtl) .property-badge {
  right: 1rem !important;
  left: auto !important;
}

:host-context(html[dir="rtl"]) .property-location,
:host-context(html[lang="ar"]) .property-location,
:host-context(.rtl) .property-location {
  right: 1rem !important;
  left: auto !important;
  text-align: right !important;
  flex-direction: row-reverse !important;
}

:host-context(html[dir="rtl"]) .property-location i,
:host-context(html[lang="ar"]) .property-location i,
:host-context(.rtl) .property-location i {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}

:host-context(html[dir="rtl"]) .card-body,
:host-context(html[lang="ar"]) .card-body,
:host-context(.rtl) .card-body {
  text-align: right !important;
  direction: rtl !important;
}

:host-context(html[dir="rtl"]) .property-price,
:host-context(html[lang="ar"]) .property-price,
:host-context(.rtl) .property-price {
  text-align: right !important;
}

/* إصلاح Header في العربية */
:host-context(html[dir="rtl"]) .simple-header,
:host-context(html[lang="ar"]) .simple-header,
:host-context(.rtl) .simple-header {
  .d-flex {
    direction: rtl;
  }

  h1 {
    text-align: right;
  }

  p {
    text-align: right;
  }
}

// Base container styles
.container-fluid {
  transition: padding 0.3s ease;
}

.property-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  border: 1px solid #f0f0f0;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
  }

  .property-image {
    position: relative;
    overflow: hidden;

    img {
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1.05);
    }
  }

  .property-badge {
    .badge {
      border-radius: 20px;
      font-weight: 600;
      letter-spacing: 0.5px;
    }
  }

  .property-location {
    div {
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 500;
    }
  }

  .property-content {
    .property-title {
      color: #2c3e50;
      font-size: 1.25rem;
      line-height: 1.4;
    }

    .property-details {
      small {
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      span {
        font-size: 0.9rem;
        color: #2c3e50;
      }
    }

    .property-price {
      h5 {
        font-size: 1.4rem;
        font-weight: 700;
      }
    }

    .btn {
      border-radius: 8px;
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
      }
    }
  }
}

// Header Styles
.display-4 {
  background: linear-gradient(135deg, #007bff, #0056b3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// Search Styles
.search-container {
  .input-group {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:focus-within {
      box-shadow: 0 6px 20px rgba(0, 123, 255, 0.2);
      transform: translateY(-2px);
    }

    .input-group-text {
      border: 1px solid #e0e0e0;
      border-right: none;
      border-radius: 8px 0 0 8px;
      padding: 0.75rem 1rem;

      i {
        font-size: 1.1rem;
      }
    }

    .form-control {
      border: 1px solid #e0e0e0;
      border-left: none;
      padding: 0.75rem 1rem;
      font-size: 1rem;

      &:focus {
        border-color: #007bff;
        box-shadow: none;
      }

      &::placeholder {
        color: #999;
        font-style: italic;
      }
    }
  }
}

// Alert Styles
.alert {
  border-radius: 12px;
  border: none;
  font-weight: 500;

  &.alert-info {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #1565c0;
  }

  &.alert-success {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    color: #2e7d32;
  }
}

// Button Styles
.btn {
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;

  &.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #0056b3, #004085);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
    }
  }

  &.btn-outline-primary {
    border: 2px solid #007bff;
    color: #007bff;
    background: transparent;

    &:hover {
      background: #007bff;
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
    }
  }
}

// Loading Spinner
.spinner-border {
  animation: spinner-border 0.75s linear infinite;
}

// Empty State
.empty-state {
  padding: 3rem 1rem;

  i {
    opacity: 0.5;
  }

  h3 {
    margin-top: 1.5rem;
    font-weight: 600;
  }

  p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }
}

// Grid spacing adjustments
.row {
  &.g-3 {
    --bs-gutter-x: 1rem;
    --bs-gutter-y: 1rem;
  }

  &.g-md-4 {
    @media (min-width: 768px) {
      --bs-gutter-x: 1.5rem;
      --bs-gutter-y: 1.5rem;
    }
  }
}

// Responsive Design
// Extra Large screens (1200px and up)
@media (min-width: 1200px) {
  .container-fluid {
    max-width: 1400px;
    margin: 0 auto;
  }

  .search-container {
    max-width: 600px !important;
  }
}

// Large screens (992px and up)
@media (min-width: 992px) and (max-width: 1199px) {
  .search-container {
    max-width: 550px !important;
  }

  .property-card {
    .property-content {
      padding: 1.25rem !important;
    }
  }
}

// Medium screens (768px and up)
@media (min-width: 768px) and (max-width: 991px) {
  .search-container {
    max-width: 500px !important;
  }

  .property-card {
    margin-bottom: 1.5rem;

    .property-content {
      padding: 1rem !important;
    }
  }

  .display-4 {
    font-size: 2.5rem;
  }
}

// Small screens (576px and up)
@media (min-width: 576px) and (max-width: 767px) {
  .property-card {
    margin-bottom: 1.5rem;
  }

  .display-4 {
    font-size: 2rem;
  }

  .property-content {
    padding: 1rem !important;

    .property-title {
      font-size: 1.1rem;
    }

    .property-price h5 {
      font-size: 1.2rem;
    }
  }

  .btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }

  .search-container {
    max-width: 100% !important;

    .input-group {
      .input-group-text {
        padding: 0.6rem 0.8rem;
      }

      .form-control {
        padding: 0.6rem 0.8rem;
        font-size: 0.9rem;
      }
    }
  }
}

// Extra Small screens (less than 576px)
@media (max-width: 575px) {
  .container-fluid {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  .display-4 {
    font-size: 1.75rem;
    text-align: center;
  }

  .text-muted {
    text-align: center;
    font-size: 1rem;
  }

  .btn-lg {
    width: 100%;
    margin-top: 1rem;
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .search-container {
    max-width: 100% !important;
    padding: 0 0.5rem;

    .input-group {
      .input-group-text {
        padding: 0.5rem 0.75rem;

        i {
          font-size: 1rem;
        }
      }

      .form-control {
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;

        &::placeholder {
          font-size: 0.8rem;
        }
      }
    }
  }

  .property-card {
    margin-bottom: 1.25rem;

    .property-image img {
      height: 180px !important;
    }

    .property-content {
      padding: 0.75rem !important;

      .property-title {
        font-size: 1rem;
        margin-bottom: 0.75rem;
      }

      .property-details {
        margin-bottom: 0.75rem;

        .row {
          gap: 0.25rem;
        }

        .col-6 {
          margin-bottom: 0.5rem;
        }

        small {
          font-size: 0.7rem;
        }

        span {
          font-size: 0.8rem;
        }
      }

      .property-price h5 {
        font-size: 1.1rem;
        margin-bottom: 0.75rem;
      }

      .btn {
        padding: 0.5rem;
        font-size: 0.85rem;
      }
    }
  }

  .empty-state {
    padding: 2rem 0.5rem;

    i {
      font-size: 3rem !important;
    }

    h3 {
      font-size: 1.25rem;
    }

    p {
      font-size: 0.9rem;
    }
  }

  // Header responsive adjustments
  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;

    .btn-outline-primary {
      align-self: center;
    }
  }
}
