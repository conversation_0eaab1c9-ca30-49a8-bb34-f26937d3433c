import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';
import { AuthService } from './auth.service';
import { AuthenticationService } from 'src/app/pages/authentication';

@Injectable({ providedIn: 'root' })
export class AuthGuard  {
  constructor(private authService: AuthService, private authenticationService: AuthenticationService, private router: Router) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    const currentUser = localStorage.getItem('authToken');
    if (currentUser) {
      // logged in so return true
      return true;
    }

    // not logged in so redirect to login page with the return url
    // Clear any existing auth data
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    this.authService.currentUserValue = undefined;
    return this.router.createUrlTree(['/home']);
    // return this.router.createUrlTree(['/authentication/login']);
  }
}

@Injectable({ providedIn: 'root' })
export class NoAuthGuard {
  constructor(private router: Router) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    const authToken = localStorage.getItem('authToken');
    const currentUser = localStorage.getItem('currentUser');

    if (authToken && currentUser) {
      // User is logged in, redirect to profile
      return this.router.createUrlTree(['/home']);
      // return this.router.createUrlTree(['/profile']);
    }

    // User is not logged in, allow access to auth pages
    return true;
  }
}
