:host {
  height: 100%;
  margin: 0;

  .flex-root {
    height: 100%;
  }
}

.page-loaded {
  app-layout {
    opacity: 1;
    transition: opacity 1s ease-in-out;
  }
}

// RTL Support for Layout
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]) {
  // Fix app wrapper direction
  .app-wrapper {
    direction: rtl;
  }

  // Mobile specific RTL fixes
  @media (max-width: 991.98px) {
    .app-page {
      direction: rtl;
    }

    .app-wrapper {
      flex-direction: row-reverse;
    }

    .app-main {
      direction: rtl;
    }

    // Fix header positioning
    .app-header {
      direction: rtl;

      .app-navbar {
        direction: rtl;
        justify-content: flex-end;
      }
    }

    // Fix content direction
    .app-content {
      direction: rtl;
    }
  }
}
