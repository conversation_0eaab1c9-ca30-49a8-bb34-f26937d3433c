<nav>
  <ul class="pagination pagination-sm">
    <li class="page-item" [class.disabled]="currentPage === 0">
      <a class="page-link" (click)="goToPage(currentPage - 1)">‹</a>
    </li>

    <li class="page-item" *ngFor="let page of pages" [class.active]="page === (currentPage + 1)">
      <a class="page-link" (click)="goToPage(page - 1)">{{ page }}</a>
    </li>

    <li class="page-item" [class.disabled]="currentPage === (totalPages - 1)">
      <a class="page-link" (click)="goToPage(currentPage + 1)">›</a>
    </li>
  </ul>
</nav>
