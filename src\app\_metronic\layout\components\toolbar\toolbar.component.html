<!--begin::Toolbar container-->
<div id="kt_app_toolbar_container" class="app-container row" [ngClass]="appToolbarContainerCSSClass">
  <ng-container *ngIf="showPageTitle()">
    <app-page-title [appPageTitleDirection]="appPageTitleDirection" [appPageTitleBreadcrumb]="appPageTitleBreadcrumb"
      [appPageTitleDescription]="appPageTitleDescription" class="page-title d-flex flex-wrap me-3"
      [ngClass]="{'flex-column justify-content-center': appPageTitleDirection === 'column', 'align-items-center': appPageTitleDirection !== 'column', appPageTitleCSSClass}">
    </app-page-title>
  </ng-container>
</div>
<!--end::Toolbar container-->
