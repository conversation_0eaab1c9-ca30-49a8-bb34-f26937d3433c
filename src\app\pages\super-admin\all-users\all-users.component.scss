// All Users component styles
.table {
  th {
    font-weight: 600;
    color: #0D47A1;
    border-bottom: 2px solid #f1f1f1;
  }

  td {
    vertical-align: middle;
    border-bottom: 1px solid #f1f1f1;
  }
}

.symbol {
  .symbol-label {
    font-size: 1rem;
    font-weight: 600;
  }
}

.btn {
  &.btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
  }
}

.badge {
  &.badge-light-success {
    background-color: rgba(34, 197, 94, 0.1);
    color: #22c55e;
  }

  &.badge-light-warning {
    background-color: rgba(251, 191, 36, 0.1);
    color: #fbbf24;
  }

  &.badge-light-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }

  &.badge-light-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
  }

  &.badge-light-primary {
    background-color: rgba(13, 71, 161, 0.1);
    color: #0d47a1;
  }

  &.badge-light-secondary {
    background-color: rgba(107, 114, 128, 0.1);
    color: #6b7280;
  }
}

.text-hover-dark-blue:hover {
  color: #1e40af !important;
}

// Responsive adjustments
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }

  .symbol-40px {
    width: 30px !important;
    height: 30px !important;
  }

  .fs-6 {
    font-size: 0.875rem !important;
  }
}
