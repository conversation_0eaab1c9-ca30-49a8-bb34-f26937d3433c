import { Routes } from '@angular/router';
import { ChatComponentComponent } from './shared/chat-component/chat-component.component';
import { PermissionGuard } from '../core/guards/permission.guard';

const Routing: Routes = [
  // {
  //   path: 'home',
  //   loadChildren: () =>
  //     import('./home/<USER>').then((m) => m.HomeModule),
  // },
  {
    path: 'client',
    canActivate: [PermissionGuard],
    data: { permission: 'client_actions' },
    loadChildren: () =>
      import('./user/client/client.module').then((m) => m.ClientModule),
  },

  {
    path: 'profile',
    canActivate: [PermissionGuard],
    data: { permission: 'view_user' },
    loadChildren: () =>
      import('./shared/profile/profile-routing.module').then(
        (m) => m.ProfileRoutingModule
      ),
  },
    {
    path: 'super-admin',
    canActivate: [PermissionGuard],
    data: { permission: 'admin_actions' },
    loadChildren: () =>
      import('../pages/super-admin/super-admin.module').then(
        (m) => m.SuperAdminModule
      ),
  },

  {
    path: 'broker',
    loadChildren: () =>
      import('./broker/broker.module').then((m) => m.BrokerModule),
  },
  {
    path: 'requests',
    canActivate: [PermissionGuard],
    data: { permission: 'list_requests' },
    loadChildren: () =>
      import('./requests/requests.module').then((m) => m.RequestsModule),
  },
  {
    path: 'developer',
    loadChildren: () =>
      import('./developer/developer.module').then((m) => m.DeveloperModule),
  },
  {
    path: 'chat',
    component: ChatComponentComponent,
  },
  {
    path: 'rating',
    loadChildren: () =>
      import('./shared/rating/rating.module').then((m) => m.RatingModule),
  },
  {
    path: 'builder',
    loadChildren: () =>
      import('./builder/builder.module').then((m) => m.BuilderModule),
  },
  {
    path: 'crafted/pages/profile',
    loadChildren: () =>
      import('../modules/profile/profile.module').then((m) => m.ProfileModule),
    // data: { layout: 'light-sidebar' },
  },
  {
    path: 'crafted/account',
    loadChildren: () =>
      import('../modules/account/account.module').then((m) => m.AccountModule),
    // data: { layout: 'dark-header' },
  },
  {
    path: 'crafted/pages/wizards',
    loadChildren: () =>
      import('../modules/wizards/wizards.module').then((m) => m.WizardsModule),
    // data: { layout: 'light-header' },
  },
  {
    path: 'crafted/widgets',
    loadChildren: () =>
      import('../modules/widgets-examples/widgets-examples.module').then(
        (m) => m.WidgetsExamplesModule
      ),
    // data: { layout: 'light-header' },
  },
  {
    path: 'apps/chat',
    loadChildren: () =>
      import('../modules/apps/chat/chat.module').then((m) => m.ChatModule),
    // data: { layout: 'light-sidebar' },
  },
  {
    path: 'apps/users',
    canActivate: [PermissionGuard],
    data: { permission: 'admin_actions' },
    loadChildren: () => import('./user/user.module').then((m) => m.UserModule),
  },
  {
    path: 'apps/roles',
    canActivate: [PermissionGuard],
    data: { permission: 'admin_actions' },
    loadChildren: () => import('./role/role.module').then((m) => m.RoleModule),
  },
  {
    path: 'apps/permissions',
    canActivate: [PermissionGuard],
    data: { permission: 'admin_actions' },
    loadChildren: () =>
      import('./permission/permission.module').then((m) => m.PermissionModule),
  },
  {
    path: '',
    redirectTo: '/home',
    pathMatch: 'full',
  },
  {
    path: '**',
    redirectTo: 'error/404',
  },
];

export { Routing };
