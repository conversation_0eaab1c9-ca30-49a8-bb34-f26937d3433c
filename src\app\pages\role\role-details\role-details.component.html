<div class="d-flex flex-column flex-lg-row">
    <!--begin::Sidebar-->
    <div class="flex-column flex-lg-row-auto w-100 w-lg-200px w-xl-300px mb-10">
        <!--begin::Card-->
        <div class="card card-flush">
            <!--begin::Card header-->
            <div class="card-header">
                <!--begin::Card title-->
                <div class="card-title">
                    <h2 class="mb-0">
                        {{ (role$ | async)?.name }}
                    </h2>
                </div>
                <!--end::Card title-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body pt-0">
                <!--begin::Permissions-->
                <div class="d-flex flex-column text-gray-600">
                    <ng-container *ngIf="role$ | async as role">
                        <ng-container *ngFor="let permission of role.permissions; let i = index">
                            <div class="d-flex align-items-center py-2" *ngIf="i < 5">
                                <span class="bullet bg-primary me-3"></span>
                                {{ permission.name }}
                            </div>
                            <div class="d-flex align-items-center py-2" *ngIf="i === 5 && role.permissions?.length && role.permissions.length > 5">
                                <span class="bullet bg-primary me-3"></span>
                                <em>and {{ role.permissions.length - 5 }} more...</em>
                            </div>
                        </ng-container>
                    </ng-container>
                </div>
                <!--end::Permissions-->
            </div>
            <!--end::Card body-->
            <!--begin::Card footer-->
            <div class="card-footer pt-0">
                <button type="button" class="btn btn-light btn-active-primary" data-role-id="developer" data-bs-toggle="modal" data-bs-target="#kt_modal_update_role">Edit Role</button>
            </div>
            <!--end::Card footer-->
        </div>
        <!--end::Card-->
    </div>
    <!--end::Sidebar-->
    <!--begin::Content-->
    <div class="flex-lg-row-fluid ms-lg-10">
        <!--begin::Card-->
        <div class="card card-flush mb-6 mb-xl-9">
            <!--begin::Card header-->
            <div class="card-header pt-5">
                <!--begin::Card title-->
                <div class="card-title">
                    <h2 class="d-flex align-items-center">Users Assigned
                        <ng-container *ngIf="role$ | async as role">
                            <span class="text-gray-600 fs-6 ms-1">({{ role.users.length }})</span>
                        </ng-container>
                    </h2>
                </div>
                <!--end::Card title-->
                <!--begin::Card toolbar-->
                <div class="card-toolbar">
                </div>
                <!--end::Card toolbar-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body pt-0">
                <!--begin::Table-->
                <app-crud [datatableConfig]="datatableConfig" (deleteEvent)="deleteUser($event)" [reload]="reloadEvent"></app-crud>
                <!--end::Table-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->
    </div>
    <!--end::Content-->
</div>