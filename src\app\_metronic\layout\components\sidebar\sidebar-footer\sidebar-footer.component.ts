import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { environment } from '../../../../../../environments/environment';
import { AuthenticationService } from 'src/app/pages/authentication';
import Swal from 'sweetalert2';
import { Router } from '@angular/router';

@Component({
  selector: 'app-sidebar-footer',
  templateUrl: './sidebar-footer.component.html',
  styleUrls: ['./sidebar-footer.component.scss'],
})
export class SidebarFooterComponent implements OnInit {
  appPreviewChangelogUrl: string = environment.appPreviewChangelogUrl;

  constructor( private authenticationService:AuthenticationService, private cd:ChangeDetectorRef, private router:Router) {}

  ngOnInit(): void {}


  logout(){
    // Clear all authentication data
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');

    // Try to call the logout API if available
    this.authenticationService.logout().subscribe(
      (response: any) => {
        console.log('logout success:', response);
        this.authenticationService.setCurrentUser(null);
        this.cd.markForCheck();
        this.router.navigate(['/authentication/login']);
      },
      (error: any) => {
        console.error('Failed to logout:', error);
        // Even if API call fails, still logout locally
        this.authenticationService.setCurrentUser(null);
        this.cd.markForCheck();
        this.router.navigate(['/authentication/login']);
      }
    );
  }
}
