import { Injectable } from '@angular/core';
import { Validators } from '@angular/forms';
import { BaseConfigService, InputConfig, StepperConfiguration } from '../base-config.service';
import {
  FLOOR_TYPES_OPTIONS,
  UNIT_FACING_TYPES_OPTIONS,
  PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
  PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
  PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
  SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
  PAYMENT_METHOD_OPTIONS,
  UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
  ACTIVITY_TYPES_OPTIONS,
  BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
  FIT_OUT_CONDITION_TYPES_OPTIONS,
  GROUND_LAYOUT_STATUS_TYPES_OPTIONS,
  UNIT_DESCRIPTION_TYPES_OPTIONS,
  UNIT_DESIGN_TYPES_OPTIONS,
  BASEMENT_UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
  ROOF_UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
  Purchase_ROOF_UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
  Purchase_BUILDING_STATUS_TYPES_OPTIONS,
  Purchase_SPECIFIC_FINISHING_STATUS_TYPES_OPTIONS,
  Purchase_Land_DELIVERY_STATUS_TYPES_OPTIONS,
  SUB_UNIT_OPTIONS,
  UNIT_VIEW_TYPES_OPTIONS,
  FINISHING_STATUS_TYPES_OPTIONS,
  DELIVERY_STATUS_TYPES_OPTIONS,
  FURNISHING_STATUS_OPTIONS,
  PURCHASE_VACATION_UNIT_VIEW_TYPES_OPTIONS,
} from '../../stepper-modal.constants';

@Injectable({
  providedIn: 'root',
})
export class PurchaseOutsideCompoundConfigService extends BaseConfigService {
  // ============================================================================
  // LOCATION INPUTS
  // ============================================================================

  /**
   * Create purchase outside compound location inputs
   */
  private createPurchaseOutsideCompoundLocationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 2,
        name: 'locationSuggestions',
        type: 'checkbox',
        label: 'Location Suggestion',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
    ];
  }

  // ============================================================================
  // UNIT INFORMATION INPUTS
  // ============================================================================

  /**
   * Create purchase outside compound unit information inputs for apartments
   */
  private createPurchaseOutsideCompoundApartmentsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Unit Area Min (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Unit Area Max (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'bedrooms',
        type: 'number',
        label: 'Bedrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathrooms',
        type: 'number',
        label: 'Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create purchase outside compound unit information inputs for duplexes
   */
  private createPurchaseOutsideCompoundDuplexesUnitInformationInputs(stepperModal: any): InputConfig[] {
    return this.createPurchaseOutsideCompoundApartmentsUnitInformationInputs(stepperModal);
  }

  /**
   * Create purchase outside compound unit information inputs for studios
   */
  private createPurchaseOutsideCompoundStudiosUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Unit Area Min (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Unit Area Max (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create purchase outside compound unit information inputs for penthouses
   */
  private createPurchaseOutsideCompoundPenthousesUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Unit Area Min (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Unit Area Max (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'bedrooms',
        type: 'number',
        label: 'Bedrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathrooms',
        type: 'number',
        label: 'Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create purchase outside compound unit information inputs for basements
   */
  private createPurchaseOutsideCompoundBasementsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Unit Area Min (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Unit Area Max (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'unitStatus',
        type: 'select',
        label: 'Unit Status',
        options: BASEMENT_UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bedrooms',
        type: 'number',
        label: 'Bedrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathrooms',
        type: 'number',
        label: 'Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create purchase outside compound unit information inputs for roofs
   */
  private createPurchaseOutsideCompoundRoofsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Unit Area Min (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Unit Area Max (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'unitStatus',
        type: 'select',
        label: 'Unit Status',
        options: Purchase_ROOF_UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingLayoutStatus',
        type: 'select',
        label: 'Building Layout Status',
        options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bedrooms',
        type: 'number',
        label: 'Bedrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathrooms',
        type: 'number',
        label: 'Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create purchase outside compound unit information inputs for standalone villas
   */
  private createPurchaseOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundArea',
        type: 'number',
        label: 'Ground Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingArea',
        type: 'number',
        label: 'Building Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingAreaSuggestions',
        type: 'checkbox',
        label: 'Building Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'unitDesign',
        type: 'select',
        label: 'Unit Design',
        options: UNIT_DESIGN_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingStatus',
        type: 'select',
        label: 'Building Status',
        options: Purchase_BUILDING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create purchase outside compound unit information inputs for administrative units
   */
  private createPurchaseOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Unit Area Min (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Unit Area Max (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: Purchase_SPECIFIC_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'shopActivity',
        type: 'text',
        label: 'Activity',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create purchase outside compound unit information inputs for medical clinics
   */
  private createPurchaseOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return this.createPurchaseOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal);
  }

  /**
   * Create purchase outside compound unit information inputs for pharmacies
   */
  private createPurchaseOutsideCompoundPharmaciesUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Unit Area Min (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Unit Area Max (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: Purchase_SPECIFIC_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'fitOutCondition',
        type: 'select',
        label: 'Fit Out Condition',
        options: FIT_OUT_CONDITION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create purchase outside compound unit information inputs for commercial stores
   */
  private createPurchaseOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal: any): InputConfig[] {
    return this.createPurchaseOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal);
  }

  /**
   * Create purchase outside compound unit information inputs for factory lands
   */
  private createPurchaseOutsideCompoundFactoryLandsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'groundStatus',
        type: 'select',
        label: 'Ground Status',
        options: GROUND_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundArea',
        type: 'number',
        label: 'Ground Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingArea',
        type: 'number',
        label: 'Building Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingAreaSuggestions',
        type: 'checkbox',
        label: 'Building Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'shopActivity',
        type: 'text',
        label: 'Activity',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'fitOutCondition',
        type: 'select',
        label: 'Fit Out Condition',
        options: FIT_OUT_CONDITION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create purchase outside compound unit information inputs for warehouses
   */
  private createPurchaseOutsideCompoundWarehousesUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'groundStatus',
        type: 'select',
        label: 'Ground Status',
        options: GROUND_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundArea',
        type: 'number',
        label: 'Ground Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingArea',
        type: 'number',
        label: 'Building Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingAreaSuggestions',
        type: 'checkbox',
        label: 'Building Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'shopActivity',
        type: 'text',
        label: 'Activity',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'fitOutCondition',
        type: 'select',
        label: 'Fit Out Condition',
        options: FIT_OUT_CONDITION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create purchase outside compound unit information inputs for residential buildings
   */
  private createPurchaseOutsideCompoundResidentialBuildingsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'groundStatus',
        type: 'select',
        label: 'Ground Status',
        options: GROUND_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundArea',
        type: 'number',
        label: 'Ground Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingArea',
        type: 'number',
        label: 'Building Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingStatus',
        type: 'select',
        label: 'Building Status',
        options: Purchase_BUILDING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingAreaSuggestions',
        type: 'checkbox',
        label: 'Building Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: Purchase_Land_DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitDescription',
        type: 'select',
        label: 'Ground Description',
        options: UNIT_DESCRIPTION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create purchase outside compound unit information inputs for commercial administrative buildings
   */
  private createPurchaseOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'groundStatus',
        type: 'select',
        label: 'Ground Status',
        options: GROUND_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundArea',
        type: 'number',
        label: 'Ground Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingArea',
        type: 'number',
        label: 'Building Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingAreaSuggestions',
        type: 'checkbox',
        label: 'Building Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitDescription',
        type: 'select',
        label: 'Ground Description',
        options: UNIT_DESCRIPTION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'activity',
        type: 'select',
        label: 'Activity',
        options: ACTIVITY_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  private createPurchaseOutsideCompoundVacationVillasInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'villageName',
        type: 'text',
        label: 'Village Name',
        validators: [Validators.required],
        visibility: () => true,
      },
       {
        step: 3,
        name: 'subUnitType',
        type: 'select',
        label: 'SubUnit Type',
        options: SUB_UNIT_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Unit Area Min (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Unit Area Max (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
     {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: PURCHASE_VACATION_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  private createPurchaseOutsideCompoundChaletsInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'villageName',
        type: 'text',
        label: 'Village Name',
        validators: [Validators.required],
        visibility: () => true,
      },
       {
        step: 3,
        name: 'subUnitType',
        type: 'select',
        label: 'SubUnit Type',
        options: SUB_UNIT_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Unit Area Min (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Unit Area Max (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'areaSuggestions',
        type: 'checkbox',
        label: 'Area Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: PURCHASE_VACATION_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: PURCHASE_DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  // ============================================================================
  // FINANCIAL INPUTS
  // ============================================================================

  /**
   * Create purchase outside compound financial inputs
   */
  private createPurchaseOutsideCompoundFinancialInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 5,
        name: 'paymentMethod',
        type: 'select',
        label: 'Payment Method',
        options: PAYMENT_METHOD_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'averageUnitPriceMin',
        type: 'number',
        label: 'Average Unit Price Min',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'averageUnitPriceMax',
        type: 'number',
        label: 'Average Unit Price Max',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'unitPriceSuggestions',
        type: 'checkbox',
        label: 'Price Suggestion',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
    ];
  }

  // ============================================================================
  // CONFIGURATION METHODS
  // ============================================================================

  /**
   * Configuration for purchase outside compound apartments
   */
  private createPurchaseOutsideCompoundApartmentsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundApartmentsUnitInformationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for purchase outside compound duplexes
   */
  private createPurchaseOutsideCompoundDuplexesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundDuplexesUnitInformationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for purchase outside compound studios
   */
  private createPurchaseOutsideCompoundStudiosConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundStudiosUnitInformationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for purchase outside compound penthouses
   */
  private createPurchaseOutsideCompoundPenthousesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundPenthousesUnitInformationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for purchase outside compound basements
   */
  private createPurchaseOutsideCompoundBasementsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundBasementsUnitInformationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for purchase outside compound roofs
   */
  private createPurchaseOutsideCompoundRoofsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundRoofsUnitInformationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for purchase outside compound standalone villas
   */
  private createPurchaseOutsideCompoundStandaloneVillasConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for purchase outside compound administrative units
   */
  private createPurchaseOutsideCompoundAdministrativeUnitsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for purchase outside compound medical clinics
   */
  private createPurchaseOutsideCompoundMedicalClinicsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for purchase outside compound pharmacies
   */
  private createPurchaseOutsideCompoundPharmaciesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundPharmaciesUnitInformationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for purchase outside compound commercial stores
   */
  private createPurchaseOutsideCompoundCommercialStoresConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for purchase outside compound factory lands
   */
  private createPurchaseOutsideCompoundFactoryLandsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundFactoryLandsUnitInformationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for purchase outside compound warehouses
   */
  private createPurchaseOutsideCompoundWarehousesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundWarehousesUnitInformationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for purchase outside compound residential buildings
   */
  private createPurchaseOutsideCompoundResidentialBuildingsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundResidentialBuildingsUnitInformationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for purchase outside compound commercial administrative buildings
   */
  private createPurchaseOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  private createPurchaseOutsideCompoundVacationVillasConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundVacationVillasInformationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  private createPurchaseOutsideCompoundChaletsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundChaletsInformationInputs(stepperModal),
      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  // ============================================================================
  // PUBLIC METHODS
  // ============================================================================

  /**
   * Get input configurations for purchase outside compound cases
   */
  getInputConfigs(stepperModal: any): StepperConfiguration[] {
    return [
      // PURCHASE OUTSIDE COMPOUND CONFIGURATIONS
      {
        key: 'purchase_sell_outside_compound_purchasing_apartments',
        value: this.createPurchaseOutsideCompoundApartmentsConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_purchasing_duplexes',
        value: this.createPurchaseOutsideCompoundDuplexesConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_purchasing_studios',
        value: this.createPurchaseOutsideCompoundStudiosConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_purchasing_penthouses',
        value: this.createPurchaseOutsideCompoundPenthousesConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_purchasing_basements',
        value: this.createPurchaseOutsideCompoundBasementsConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_purchasing_roofs',
        value: this.createPurchaseOutsideCompoundRoofsConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_purchasing_standalone_villas',
        value: this.createPurchaseOutsideCompoundStandaloneVillasConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_purchasing_administrative_units',
        value: this.createPurchaseOutsideCompoundAdministrativeUnitsConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_purchasing_medical_clinics',
        value: this.createPurchaseOutsideCompoundMedicalClinicsConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_purchasing_pharmacies',
        value: this.createPurchaseOutsideCompoundPharmaciesConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_purchasing_commercial_stores',
        value: this.createPurchaseOutsideCompoundCommercialStoresConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_purchasing_factory_lands',
        value: this.createPurchaseOutsideCompoundFactoryLandsConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_purchasing_warehouses',
        value: this.createPurchaseOutsideCompoundWarehousesConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_purchasing_residential_buildings',
        value: this.createPurchaseOutsideCompoundResidentialBuildingsConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_purchasing_residential_lands',
        value: this.createPurchaseOutsideCompoundResidentialBuildingsConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_purchasing_commercial_administrative_buildings',
        value: this.createPurchaseOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_purchasing_vacation_villa',
        value: this.createPurchaseOutsideCompoundVacationVillasConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_purchasing_chalets',
        value: this.createPurchaseOutsideCompoundChaletsConfig(stepperModal),
      },
    ];
  }
}
