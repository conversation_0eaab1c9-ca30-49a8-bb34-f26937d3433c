.card {
  max-width: 600px;
  margin: 0 auto;
  border-radius: 15px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  border: none;

  .card-body {
    padding: 2rem;
  }
}

.text-dark-blue {
  color: #1e1e2d;
}

.btn-dark-blue {
  background-color: #1e1e2d;
  color: #ffffff;
}

.btn-navy {
  background-color: #1e1e7c;
  color: #ffffff;
  border: none;
  &:hover {
    background-color: #16165a;
  }
  &:disabled {
    background-color: #9999c9;
  }
}

.progress {
  border-radius: 30px;
}

.progress-bar {
  border-radius: 30px;
}

.cursor-pointer {
  cursor: pointer;
}

// Custom styling for the form
.form-control {
  border-radius: 8px;
  padding: 0.75rem 1rem;
}

.form-select {
  border-radius: 8px;
  padding: 0.75rem 1rem;
}

// Dropdown styling
.dropdown {
  .btn-outline-secondary {
    border-radius: 8px;
    border: 1px solid #e4e6ef;
    background-color: #f5f8fa;
    color: #5e6278;
    padding: 0.75rem 1rem;

    &:hover,
    &:focus {
      background-color: #f5f8fa;
      border-color: #e4e6ef;
    }

    &::after {
      display: none;
    }
  }

  .dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);
    padding: 0.5rem 0;

    .dropdown-item {
      padding: 0.75rem 1.25rem;
      cursor: pointer;

      &:hover {
        background-color: #f5f8fa;
      }
    }
  }
}

// Custom styling for buttons
.btn-primary {
  border-radius: 30px;
  background-color: #3f51b5;
  border-color: #3f51b5;
}

.btn-primary:hover {
  background-color: #303f9f;
  border-color: #303f9f;
}

.btn-primary:disabled {
  background-color: #9fa8da;
  border-color: #9fa8da;
}

// Upload card styling
.upload-card-container {
  .upload-card {
    transition: all 0.3s ease;
    border-radius: 15px;
    border: 2px dashed #e4e6ef;
    background-color: #f8f9fa;

    label {
      cursor: pointer;
      margin-bottom: 0;
      padding: 2rem 1rem;

      .upload-icon {
        margin-bottom: 1rem;

        i {
          color: #6c757d;
          transition: color 0.3s ease;
        }
      }

      .upload-content {
        .upload-title {
          color: #495057;
          font-weight: 600;
          font-size: 1.1rem;
        }

        .upload-subtitle {
          color: #6c757d;
          font-size: 0.9rem;
        }

        .badge {
          font-size: 0.8rem;
          padding: 0.4rem 0.8rem;
        }
      }
    }

    &:hover {
      border-color: #007bff;
      background-color: #f0f8ff;
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(0, 123, 255, 0.15);

      label {
        .upload-icon i {
          color: #007bff;
        }

        .upload-content .upload-title {
          color: #007bff;
        }
      }
    }

    // When files are selected
    &.has-files {
      border-color: #28a745;
      background-color: #f8fff9;

      label {
        .upload-icon i {
          color: #28a745;
        }

        .upload-content .upload-title {
          color: #28a745;
        }
      }
    }
  }
}

// Enhanced Custom checkbox styling
.custom-checkbox-container {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  position: relative;

  .custom-checkbox-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    width: 0;
    height: 0;

    &:checked + .custom-checkbox-label {
      .custom-checkbox-box {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border-color: #007bff;
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);

        .custom-checkbox-icon {
          opacity: 1;
          transform: scale(1) rotate(0deg);
          animation: checkmarkBounce 0.3s ease-out;
        }
      }

      .custom-checkbox-text {
        color: #007bff;
        font-weight: 600;
        transform: translateX(2px);
      }

      &::before {
        opacity: 1;
        transform: scale(1);
      }
    }

    &:focus + .custom-checkbox-label {
      .custom-checkbox-box {
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25), 0 2px 8px rgba(0, 123, 255, 0.15);
        border-color: #007bff;
      }

      &::before {
        opacity: 0.7;
        transform: scale(1);
      }
    }

    &:disabled + .custom-checkbox-label {
      opacity: 0.6;
      cursor: not-allowed;

      .custom-checkbox-box {
        background-color: #f8f9fa;
        border-color: #dee2e6;
      }
    }
  }

  .custom-checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    padding: 0.5rem 0;
    border-radius: 8px;
    width: 100%;

    // Subtle background on hover
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -0.5rem;
      right: -0.5rem;
      bottom: 0;
      background: linear-gradient(90deg, rgba(0, 123, 255, 0.05) 0%, rgba(0, 123, 255, 0.02) 100%);
      border-radius: 8px;
      opacity: 0;
      transform: scale(0.95);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: -1;
    }

    .custom-checkbox-box {
      width: 22px;
      height: 22px;
      border: 2px solid #dee2e6;
      border-radius: 6px;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 14px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      flex-shrink: 0;

      // Subtle inner shadow
      box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);

      // Ripple effect
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(0, 123, 255, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: all 0.3s ease;
      }

      .custom-checkbox-icon {
        color: #fff;
        font-size: 13px;
        opacity: 0;
        transform: scale(0.3) rotate(-45deg);
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        z-index: 2;
      }
    }

    .custom-checkbox-content {
      display: flex;
      flex-direction: column;
      flex: 1;
    }

    .custom-checkbox-text {
      color: #495057;
      font-size: 15px;
      font-weight: 500;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      line-height: 1.4;
      margin-bottom: 0.25rem;
    }

    .custom-checkbox-description {
      color: #6c757d;
      font-size: 13px;
      line-height: 1.3;
      margin-top: 0.125rem;
      transition: color 0.3s ease;
    }

    &:hover {
      &::before {
        opacity: 1;
        transform: scale(1);
      }

      .custom-checkbox-box {
        border-color: #007bff;
        background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
        transform: translateY(-1px);
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 123, 255, 0.15);

        &::after {
          width: 100%;
          height: 100%;
          opacity: 0.1;
        }
      }

      .custom-checkbox-text {
        color: #007bff;
        transform: translateX(2px);
      }

      .custom-checkbox-description {
        color: #007bff;
      }
    }

    &:active {
      .custom-checkbox-box {
        transform: scale(0.95);
      }
    }
  }

  // Error state styling
  &.has-error {
    .custom-checkbox-label {
      .custom-checkbox-box {
        border-color: #dc3545;
        background: linear-gradient(135deg, #fff5f5 0%, #ffeaea 100%);
        box-shadow: inset 0 1px 3px rgba(220, 53, 69, 0.1);
      }

      .custom-checkbox-text {
        color: #dc3545;
      }

      .custom-checkbox-description {
        color: #dc3545;
        opacity: 0.8;
      }

      &:hover {
        .custom-checkbox-box {
          border-color: #dc3545;
          background: linear-gradient(135deg, #fff0f0 0%, #ffe5e5 100%);
          box-shadow: inset 0 1px 3px rgba(220, 53, 69, 0.15), 0 2px 8px rgba(220, 53, 69, 0.15);
        }
      }
    }
  }
}

// Enhanced error feedback styling
.checkbox-error-feedback {
  display: flex;
  align-items: center;
  margin-top: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: linear-gradient(90deg, rgba(220, 53, 69, 0.05) 0%, rgba(220, 53, 69, 0.02) 100%);
  border: 1px solid rgba(220, 53, 69, 0.2);
  border-radius: 6px;
  color: #dc3545;
  font-size: 0.875rem;
  animation: errorSlideIn 0.3s ease-out;

  i {
    color: #dc3545;
    margin-right: 0.25rem;
  }

  span {
    flex: 1;
  }
}

// Error slide-in animation
@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Checkmark bounce animation
@keyframes checkmarkBounce {
  0% {
    transform: scale(0.3) rotate(-45deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

// Enhanced form-check styling for multiselect options
.enhanced-multiselect {
  .multiselect-controls {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    padding: 1rem;

    .form-check {
      margin-bottom: 0;
      padding: 0.75rem 1rem;
      border-radius: 8px;
      background: white;
      border: 1px solid #e9ecef;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
        border-color: #007bff;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
      }

      .form-check-input {
        width: 20px;
        height: 20px;
        margin-top: 0.125rem;
        margin-right: 0.85rem;
        border: 2px solid #dee2e6;
        border-radius: 6px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        flex-shrink: 0;

        &:checked {
          background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
          border-color: #3927d8;
          box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
          transform: scale(1.05);
          background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
          background-position: calc(50% + 20px) center;
        }

        &:indeterminate {
          background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
          border-color: #6c757d;
          box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
          background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
          background-position: calc(50% + 20px) center;
        }

        &:focus {
          box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
          outline: none;
        }

        &:hover {
          border-color: #007bff;
          transform: scale(1.05);
        }
      }

      .form-check-label {
        font-size: 1rem;
        font-weight: 700;
        color: #007bff;
        cursor: pointer;
        transition: all 0.3s ease;
        flex: 1;

        &:hover {
          color: #0056b3;
          transform: translateX(2px);
        }
      }
    }
  }

  .multiselect-options-container {
    padding: 0.5rem 0;

    .multiselect-option {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border-radius: 8px;
      margin: 0.25rem 0.75rem;

      &:hover {
        background: linear-gradient(135deg, #f0f8ff 0%, #e3f2fd 100%);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
      }

      .form-check {
        margin-bottom: 0;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        background: white;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          border-color: #007bff;
          background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
        }

        .form-check-input {
          width: 20px;
          height: 20px;
          margin-top: 0.125rem;
          margin-right: 0.75rem;
          border: 2px solid #dee2e6;
          border-radius: 6px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
          flex-shrink: 0;

          &:checked {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border-color: #007bff;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
            transform: scale(1.05);
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
            background-position: calc(50% + 20px) center;
          }

          &:focus {
            box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
            outline: none;
          }

          &:hover {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.05);
            transform: scale(1.05);
          }
        }

        .multiselect-option-label {
          font-size: 0.9rem;
          font-weight: 500;
          color: #495057;
          cursor: pointer;
          user-select: none;
          transition: all 0.3s ease;
          line-height: 1.4;
          flex: 1;

          &.fw-bold {
            color: #007bff;
            font-weight: 700;
          }

          &:hover {
            color: #007bff;
            transform: translateX(2px);
          }
        }

        &:has(.form-check-input:checked) {
          background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
          border-color: #007bff;
          box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);

          .multiselect-option-label {
            color: #007bff;
            font-weight: 700;
          }
        }
      }
    }
  }
}

// Enhanced MultiSelect Styling (updated with checkbox improvements)
.enhanced-multiselect {
  .enhanced-multiselect-button {
    border-radius: 12px;
    border: 2px solid #e9ecef;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    color: #495057;
    padding: 1rem 1.25rem;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

    &:hover,
    &:focus {
      background: linear-gradient(135deg, #f0f8ff 0%, #e3f2fd 100%);
      border-color: #007bff;
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 123, 255, 0.15);
    }

    &.has-selections {
      background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
      border-color: #007bff;
      color: white;
      box-shadow: 0 4px 16px rgba(0, 123, 255, 0.3);

      .multiselect-text {
        color: white;
        font-weight: 600;
      }

      .badge {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        font-weight: 700;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      .multiselect-icon {
        color: white;
      }
    }

    &::after {
      display: none;
    }

    .multiselect-display {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0;

      .multiselect-text {
        flex: 1;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .badge {
        flex-shrink: 0;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
      }
    }

    .multiselect-icon {
      transition: transform 0.2s ease;
      margin-left: 0.5rem;
    }

    &[aria-expanded="true"] .multiselect-icon {
      transform: rotate(180deg);
    }
  }

  .enhanced-multiselect-menu {
    border-radius: 16px;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
    padding: 0;
    border: none;
    min-width: 100%;
    max-width: none;
    background: white;
    margin-top: 8px;
    overflow: hidden;

    .multiselect-search-container {
      background-color: #f8f9fa;
      border-bottom: 1px solid #e4e6ef;

      .input-group {
        .input-group-text {
          border-radius: 6px 0 0 6px;
        }

        .form-control {
          border-radius: 0 6px 6px 0;
          border: none;

          &:focus {
            box-shadow: none;
            border-color: transparent;
          }
        }

        .btn {
          border-radius: 0 6px 6px 0;
        }
      }
    }

    .multiselect-footer {
      background-color: #f8f9fa;
      border-top: 1px solid #e4e6ef;

      .selected-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 0.25rem;
        max-width: 200px;
        overflow: hidden;

        .badge {
          display: flex;
          align-items: center;
          max-width: 120px;

          &:nth-child(n+4) {
            display: none;
          }

          .btn-close {
            margin-left: 0.25rem;
            padding: 0;
            width: 0.6em;
            height: 0.6em;
            opacity: 0.7;

            &:hover {
              opacity: 1;
            }
          }
        }
      }
    }
  }
}

// Animation for dropdown
.enhanced-multiselect .dropdown-menu {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive adjustments
@media (max-width: 576px) {
  .enhanced-multiselect {
    .enhanced-multiselect-menu {
      .multiselect-footer {
        .selected-tags {
          max-width: 150px;

          .badge {
            max-width: 80px;
            font-size: 0.65em;
          }
        }
      }
    }
  }

  .custom-checkbox-container {
    margin-bottom: 1.25rem;

    .custom-checkbox-label {
      padding: 0.375rem 0;

      .custom-checkbox-box {
        width: 20px;
        height: 20px;
        margin-right: 12px;

        .custom-checkbox-icon {
          font-size: 12px;
        }
      }

      .custom-checkbox-text {
        font-size: 14px;
      }

      .custom-checkbox-description {
        font-size: 12px;
      }
    }
  }

  .checkbox-error-feedback {
    padding: 0.375rem 0.5rem;
    font-size: 0.8125rem;
  }
}

// Enhanced accessibility for reduced motion
@media (prefers-reduced-motion: reduce) {
  .custom-checkbox-container {
    .custom-checkbox-label {
      transition: none;

      &::before {
        transition: opacity 0.2s ease;
      }

      .custom-checkbox-box {
        transition: border-color 0.2s ease, background 0.2s ease;

        &::after {
          transition: none;
        }

        .custom-checkbox-icon {
          transition: opacity 0.2s ease;
        }
      }

      .custom-checkbox-text,
      .custom-checkbox-description {
        transition: color 0.2s ease;
      }
    }
  }

  @keyframes checkmarkBounce {
    to {
      transform: scale(1) rotate(0deg);
      opacity: 1;
    }
  }

  @keyframes errorSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

// Enhanced Other Accessories Animations
.enhanced-multiselect {
  // Enhanced animations
  .dropdown-menu {
    animation: slideDownFade 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  // Custom scrollbar for options
  .multiselect-options-container {
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
      border-radius: 10px;
      border: 2px solid #f1f3f4;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    }
  }
}

// Enhanced animations
@keyframes slideDownFade {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
