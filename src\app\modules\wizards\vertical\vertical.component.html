<div class="
    stepper stepper-pills stepper-column
    d-flex
    flex-column flex-xl-row flex-row-fluid
  " id="kt_create_account_stepper">
  <!--begin::Aside-->
  <div
    class="card d-flex justify-content-center justify-content-xl-start flex-row-auto w-100 w-xl-300px w-xxl-400px me-9">
    <div class="card-body px-6 px-lg-10 px-xxl-15 py-20">
      <div class="stepper-nav">

        <div class="stepper-item" [ngClass]="{
            current: currentStep$.value === 1,
            completed: currentStep$.value > 1
          }">
          <!--begin::Wrapper-->
          <div class="stepper-wrapper">
            <!--begin::Icon-->
            <div class="stepper-icon w-40px h-40px">
              <i class="stepper-check fas fa-check"></i>
              <span class="stepper-number">1</span>
            </div>
            <!--end::Icon-->

            <!--begin::Label-->
            <div class="stepper-label">
              <h3 class="stepper-title">
                Account Type
              </h3>

              <div class="stepper-desc fw-semibold">
                Setup Your Account Details
              </div>
            </div>
            <!--end::Label-->
          </div>
          <!--end::Wrapper-->

          <!--begin::Line-->
          <div class="stepper-line h-40px"></div>
          <!--end::Line-->
        </div>

        <div class="stepper-item" [ngClass]="{
            current: currentStep$.value === 2,
            completed: currentStep$.value > 2
          }">
          <!--begin::Wrapper-->
          <div class="stepper-wrapper">
            <!--begin::Icon-->
            <div class="stepper-icon w-40px h-40px">
              <i class="stepper-check fas fa-check"></i>
              <span class="stepper-number">2</span>
            </div>
            <!--end::Icon-->

            <!--begin::Label-->
            <div class="stepper-label">
              <h3 class="stepper-title">
                Account Settings
              </h3>
              <div class="stepper-desc fw-semibold">
                Setup Your Account Settings
              </div>
            </div>
            <!--end::Label-->
          </div>
          <!--end::Wrapper-->

          <!--begin::Line-->
          <div class="stepper-line h-40px"></div>
          <!--end::Line-->
        </div>

        <div class="stepper-item" [ngClass]="{
            current: currentStep$.value === 3,
            completed: currentStep$.value > 3
          }">
          <!--begin::Wrapper-->
          <div class="stepper-wrapper">
            <!--begin::Icon-->
            <div class="stepper-icon w-40px h-40px">
              <i class="stepper-check fas fa-check"></i>
              <span class="stepper-number">3</span>
            </div>
            <!--end::Icon-->

            <!--begin::Label-->
            <div class="stepper-label">
              <h3 class="stepper-title">
                Business Info
              </h3>
              <div class="stepper-desc fw-semibold">
                Your Business Related Info
              </div>
            </div>
            <!--end::Label-->
          </div>
          <!--end::Wrapper-->

          <!--begin::Line-->
          <div class="stepper-line h-40px"></div>
          <!--end::Line-->
        </div>

        <div class="stepper-item" [ngClass]="{
            current: currentStep$.value === 4,
            completed: currentStep$.value > 4
          }">
          <!--begin::Wrapper-->
          <div class="stepper-wrapper">
            <!--begin::Icon-->
            <div class="stepper-icon w-40px h-40px">
              <i class="stepper-check fas fa-check"></i>
              <span class="stepper-number">4</span>
            </div>
            <!--end::Icon-->

            <!--begin::Label-->
            <div class="stepper-label">
              <h3 class="stepper-title">
                Billing Details
              </h3>
              <div class="stepper-desc fw-semibold">
                Set Your Payment Methods
              </div>
            </div>
            <!--end::Label-->
          </div>
          <!--end::Wrapper-->

          <!--begin::Line-->
          <div class="stepper-line h-40px"></div>
          <!--end::Line-->
        </div>

        <div class="stepper-item" [ngClass]="{ current: currentStep$.value === 5 }">
          <!--begin::Wrapper-->
          <div class="stepper-wrapper">
            <!--begin::Icon-->
            <div class="stepper-icon w-40px h-40px">
              <i class="stepper-check fas fa-check"></i>
              <span class="stepper-number">5</span>
            </div>
            <!--end::Icon-->

            <!--begin::Label-->
            <div class="stepper-label">
              <h3 class="stepper-title">
                Completed
              </h3>
              <div class="stepper-desc fw-semibold">
                Woah, we are here
              </div>
            </div>
            <!--end::Label-->
          </div>
          <!--end::Wrapper-->
        </div>
      </div>
    </div>
  </div>
  <!--begin::Aside-->

  <!--begin::Content-->
  <div class="d-flex flex-row-fluid flex-center bg-body rounded">
    <form class="py-20 w-100 w-xl-700px px-9" noValidate id="kt_create_account_form">
      <div class="current">
        <ng-container *ngIf="currentStep$.value === 1">
          <app-step1 class="w-100" [updateParentModel]="updateAccount" [defaultValues]="account$.value"></app-step1>
        </ng-container>

        <ng-container *ngIf="currentStep$.value === 2">
          <app-step2 class="w-100" [updateParentModel]="updateAccount" [defaultValues]="account$.value"></app-step2>
        </ng-container>

        <ng-container *ngIf="currentStep$.value === 3">
          <app-step3 class="w-100" [updateParentModel]="updateAccount" [defaultValues]="account$.value"></app-step3>
        </ng-container>

        <ng-container *ngIf="currentStep$.value === 4">
          <app-step4 class="w-100" [updateParentModel]="updateAccount" [defaultValues]="account$.value"></app-step4>
        </ng-container>

        <ng-container *ngIf="currentStep$.value === 5">
          <app-step5 class="w-100"></app-step5>
        </ng-container>
      </div>

      <div class="d-flex flex-stack pt-10">
        <div class="mr-2">
          <ng-container *ngIf="currentStep$.value !== 1">
            <button type="button" class="btn btn-lg btn-light-primary me-3" (click)="prevStep()">
              <app-keenicon name="arrow-left" class="fs-3 ms-1"></app-keenicon>
              Back
            </button>
          </ng-container>
        </div>

        <ng-container *ngIf="currentStep$.value !== formsCount">
          <div>
            <button type="button" class="btn btn-lg btn-primary me-3" [disabled]="!isCurrentFormValid$.value"
              (click)="nextStep()">
              <span class="indicator-label">
                <ng-container *ngIf="currentStep$.value < formsCount - 1">
                  Continue {{ " " }}
                  <app-keenicon name="arrow-right" class="fs-3 ms-2 me-0"></app-keenicon>
                </ng-container>
                <ng-container *ngIf="currentStep$.value === formsCount - 1">
                  Submit
                </ng-container>
              </span>
            </button>
          </div>
        </ng-container>
      </div>
    </form>
  </div>
  <!--end::Content-->
</div>
