import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';
import { ProjectsService } from '../../../services/projects.service';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import Swal from 'sweetalert2';
import { ActivatedRoute, Router } from '@angular/router';
import { saveAs } from 'file-saver';

@Component({
  selector: 'app-models',
  templateUrl: './models.component.html',
  styleUrl: './models.component.scss',
})
export class ModelsComponent extends BaseGridComponent implements OnInit {
  user: any  ;

  projectId: number | null = null;

  showEmptyCard = false;
  appliedFilters: any = {};
  searchText: string = '';
  private searchTimeout: any;
  isFilterDropdownVisible = false;

  constructor(
    protected cd: ChangeDetectorRef,
    private projectsService: ProjectsService,
    private route: ActivatedRoute,
    private router: Router
  ) {
    super(cd);
    // this.setService(ProjectsService);
    this.orderBy = 'id';
    this.orderDir = 'desc';
  }

  ngOnInit(): void {
    super.ngOnInit();

    // Get user from localStorage
    const userJson = localStorage.getItem('currentUser');
    this.user = userJson ? JSON.parse(userJson) : null;

    this.route.queryParams.subscribe((params) => {
      this.projectId = params['projectId'] ? +params['projectId'] : null;
      console.log('Project ID:', this.projectId);
      console.log('User role:', this.user?.role);
      this.page.filters = { ...this.page.filters, projectId: this.projectId };
      this.reloadTable(this.page);
    });
  }

  onSearchTextChange(value: string): void {
    clearTimeout(this.searchTimeout); // Clear previous timeout
    this.searchTimeout = setTimeout(() => {
      this.page.filters = { ...this.page.filters, searchCode: value.trim() };
      console.log(this.page.filters);
      this.reloadTable(this.page);
    }, 300);
  }

  toggleFilterDropdown() {
    this.isFilterDropdownVisible = !this.isFilterDropdownVisible;
  }

  onFiltersApplied(filters: any) {
    this.toggleFilterDropdown();
    this.page.filters = { ...this.page.filters, ...filters };

    this.reloadTable(this.page);
  }

  async reloadTable(pageInfo: any) {
    this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;
    this.loading = true;
    await this.projectsService.getAllModels(this.page).subscribe(
      (response: any) => {
        console.log(response.data);
        this.rows = Array.isArray(response.data) ? response.data : [];
        this.rows = [...this.rows];

        this.page.totalElements = response.count;
        this.page.count = Math.ceil(response.count / this.page.size);

        // Update empty card visibility
        this.updateEmptyCardVisibility();

        this.cd.markForCheck();
        this.loading = false;

        this.afterGridLoaded();
        MenuComponent.reinitialization();
      },
      (error: any) => {
        console.log(error);
        this.rows = [];
        this.updateEmptyCardVisibility();
        this.cd.markForCheck();
        this.loading = false;
        Swal.fire('Failed to load data. please try again later.', '', 'error');
      }
    );
  }

  updateEmptyCardVisibility() {
    this.showEmptyCard = this.rows.length === 0;
  }

  downloadModel() {
    this.projectsService.downloadModel().subscribe({
      next: (blob: Blob) => {
        saveAs(blob, 'models-template.xlsx');
      },
      error: (err) => {
        console.error('Download error:', err);
        Swal.fire(
          'Error',
          'Failed to download template. Please try again.',
          'error'
        );
      },
    });
  }

  onFileSelected(event: any) {
    const file = event.target.files[0];
    if (file) {
      console.log('File selected:', file.name);
      this.handleFileUpload(file);
    }
  }

  handleFileUpload(file: File) {
    console.log('Uploading file:', file.name);
    this.projectsService.uploadModel(file, this.projectId).subscribe({
      next: (response) => {
        Swal.fire('Success', 'Models uploaded successfully!', 'success').then(
          () => {
            this.showEmptyCard = false;
            this.page.filters = {
              ...this.page.filters,
              projectId: this.projectId,
            };
            this.reloadTable(this.page);
          }
        );
      },
      error: (error) => {
        Swal.fire(
          'Error',
          'Failed to upload models. Please try again.',
          'error'
        );
      },
    });
  }
}
