<div class="card mb-5 mb-xl-10">
  <div
    class="card-header border-0 cursor-pointer"
    role="button"
    data-bs-toggle="collapse"
    data-bs-target="#kt_account_signin_method"
  >
    <div class="card-title m-0">
      <h3 class="fw-bolder m-0">Sign-in Method</h3>
    </div>
  </div>
  <div id="kt_account_signin_method" class="collapse show">
    <div class="card-body border-top p-9">
      <ng-container *ngIf="!showChangeEmailForm">
        <div class="d-flex flex-wrap align-items-center">
          <div id="kt_signin_email" class="false">
            <div class="fs-6 fw-bolder mb-1">Email Address</div>
            <div class="fw-bold text-gray-600">support&#64;keenthemes.com</div>
          </div>
          <div id="kt_signin_email_edit" class="flex-row-fluid d-none">
            <form id="kt_signin_change_email" class="form" novalidate="">
              <div class="row mb-6">
                <div class="col-lg-6 mb-4 mb-lg-0">
                  <div class="fv-row mb-0">
                    <label
                      for="emailaddress"
                      class="form-label fs-6 fw-bolder mb-3"
                      >Enter New Email Address</label
                    ><input
                      type="email"
                      class="form-control form-control-lg form-control-solid"
                      id="emailaddress"
                      placeholder="Email Address"
                      name="newEmail"
                      value="<EMAIL>"
                    />
                  </div>
                </div>
                <div class="col-lg-6">
                  <div class="fv-row mb-0">
                    <label
                      for="confirmemailpassword"
                      class="form-label fs-6 fw-bolder mb-3"
                      >Confirm Password</label
                    ><input
                      type="password"
                      class="form-control form-control-lg form-control-solid"
                      id="confirmemailpassword"
                      name="confirmPassword"
                      value=""
                    />
                    <div class="fv-plugins-message-container">
                      <div class="fv-help-block">Password is required</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="d-flex">
                <button
                  id="kt_signin_submit"
                  type="submit"
                  class="btn btn-primary me-2 px-6"
                >
                  Update Email</button
                ><button
                  id="kt_signin_cancel"
                  type="button"
                  class="btn btn-color-gray-500 btn-active-light-primary px-6"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
          <div id="kt_signin_email_button" class="ms-auto false">
            <button
              class="btn btn-light btn-active-light-primary"
              (click)="toggleEmailForm(true)"
            >
              Change Email
            </button>
          </div>
        </div>
      </ng-container>

      <ng-container *ngIf="showChangeEmailForm">
        <div class="d-flex flex-wrap align-items-center">
          <div id="kt_signin_email" class="d-none">
            <div class="fs-6 fw-bolder mb-1">Email Address</div>
            <div class="fw-bold text-gray-600">support&#64;keenthemes.com</div>
          </div>
          <div id="kt_signin_email_edit" class="flex-row-fluid false">
            <form id="kt_signin_change_email" class="form" novalidate="">
              <div class="row mb-6">
                <div class="col-lg-6 mb-4 mb-lg-0">
                  <div class="fv-row mb-0">
                    <label
                      for="emailaddress"
                      class="form-label fs-6 fw-bolder mb-3"
                      >Enter New Email Address</label
                    ><input
                      type="email"
                      class="form-control form-control-lg form-control-solid"
                      id="emailaddress"
                      placeholder="Email Address"
                      name="newEmail"
                      value="<EMAIL>"
                    />
                  </div>
                </div>
              </div>
              <div class="d-flex">
                <button
                  id="kt_signin_submit"
                  type="button"
                  class="btn btn-primary me-2 px-6"
                  (click)="saveEmail()"
                >
                  <ng-container *ngIf="!isLoading"> Update Email </ng-container>
                  <ng-container *ngIf="isLoading">
                    <span class="indicator-progress" [style.display]="'block'">
                      Please wait...{{ " " }}
                      <span
                        class="
                          spinner-border spinner-border-sm
                          align-middle
                          ms-2
                        "
                      ></span>
                    </span>
                  </ng-container></button
                ><button
                  id="kt_signin_cancel"
                  type="button"
                  class="btn btn-color-gray-500 btn-active-light-primary px-6"
                  (click)="toggleEmailForm(false)"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      </ng-container>

      <div class="separator separator-dashed my-6"></div>

      <ng-container *ngIf="!showChangePasswordForm">
        <div class="d-flex flex-wrap align-items-center mb-10">
          <div id="kt_signin_password" class="false">
            <div class="fs-6 fw-bolder mb-1">Password</div>
            <div class="fw-bold text-gray-600">************</div>
          </div>
          <div id="kt_signin_password_button" class="ms-auto false">
            <button
              class="btn btn-light btn-active-light-primary"
              (click)="togglePasswordForm(true)"
            >
              Reset Password
            </button>
          </div>
        </div>
      </ng-container>

      <ng-container *ngIf="showChangePasswordForm">
        <div class="d-flex flex-wrap align-items-center mb-10">
          <div id="kt_signin_password_edit" class="flex-row-fluid false">
            <form id="kt_signin_change_password" class="form" novalidate="">
              <div class="row mb-1">
                <div class="col-lg-4">
                  <div class="fv-row mb-0">
                    <label
                      for="currentpassword"
                      class="form-label fs-6 fw-bolder mb-3"
                      >Current Password</label
                    ><input
                      type="password"
                      class="form-control form-control-lg form-control-solid"
                      id="currentpassword"
                      name="currentPassword"
                      value="1234"
                    />
                  </div>
                </div>
                <div class="col-lg-4">
                  <div class="fv-row mb-0">
                    <label
                      for="newpassword"
                      class="form-label fs-6 fw-bolder mb-3"
                      >New Password</label
                    ><input
                      type="password"
                      class="form-control form-control-lg form-control-solid"
                      id="newpassword"
                      name="newPassword"
                      value="1234"
                    />
                  </div>
                </div>
                <div class="col-lg-4">
                  <div class="fv-row mb-0">
                    <label
                      for="confirmpassword"
                      class="form-label fs-6 fw-bolder mb-3"
                      >Confirm New Password</label
                    ><input
                      type="password"
                      class="form-control form-control-lg form-control-solid"
                      id="confirmpassword"
                      name="passwordConfirmation"
                      value="1234"
                    />
                  </div>
                </div>
              </div>
              <div class="form-text mb-5">
                Password must be at least 8 character and contain symbols
              </div>
              <div class="d-flex">
                <button
                  id="kt_password_submit"
                  type="button"
                  class="btn btn-primary me-2 px-6"
                  (click)="savePassword()"
                >
                  <ng-container *ngIf="!isLoading"
                    >Update Password</ng-container
                  >
                  <ng-container *ngIf="isLoading">
                    <span class="indicator-progress" [style.display]="'block'">
                      Please wait...{{ " " }}
                      <span
                        class="
                          spinner-border spinner-border-sm
                          align-middle
                          ms-2
                        "
                      ></span>
                    </span>
                  </ng-container></button
                ><button
                  id="kt_password_cancel"
                  type="button"
                  class="btn btn-color-gray-500 btn-active-light-primary px-6"
                  (click)="togglePasswordForm(false)"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
          <div id="kt_signin_password_button" class="ms-auto d-none">
            <button class="btn btn-light btn-active-light-primary">
              Reset Password
            </button>
          </div>
        </div>
      </ng-container>

      <div
        class="
          notice
          d-flex
          bg-light-primary
          rounded
          border-primary border border-dashed
          p-6
        "
      >
        <app-keenicon name="shield-tick" class="fs-2 text-primary me-4"></app-keenicon>
        <div class="d-flex flex-stack flex-grow-1 flex-wrap flex-md-nowrap">
          <div class="mb-3 mb-md-0 fw-bold">
            <h4 class="text-gray-800 fw-bolder">Secure Your Account</h4>
            <div class="fs-6 text-gray-600 pe-7">
              Two-factor authentication adds an extra layer of security to your
              account. To log in, in addition you'll need to provide a 6 digit
              code
            </div>
          </div>
          <a
            class="
              btn btn-primary
              px-6
              align-self-center
              text-nowrap
              cursor-pointer
            "
            data-bs-toggle="modal"
            data-bs-target="#kt_modal_two_factor_authentication"
            >Enable</a
          >
        </div>
      </div>
    </div>
  </div>
</div>
