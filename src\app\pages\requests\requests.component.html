<div class="mb-5 mt-0">
  <app-broker-title></app-broker-title>
</div>

<div class="card mb-5 mb-xl-5">
  <div class="card-body pt-3 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap mb-3">
      <div class="flex-grow-1">
        <div
          class="d-flex justify-content-between align-items-start flex-wrap mb-2"
        >
          <div class="d-flex my-4">
            <h1 class="text-dark-blue fs-2 fw-bolder me-1">Requests</h1>
          </div>

          <div class="d-flex my-4">
            <form
              data-kt-search-element="form"
              class="w-300px position-relative mb-3"
              autocomplete="off"
              (input)="onSearchChanged($event)"
            >
              <app-keenicon
                name="magnifier"
                class="fs-2 fs-lg-1 text-gray-500 position-absolute top-50 translate-middle-y ms-3"
                type="outline"
              ></app-keenicon>
              <input
                type="text"
                class="form-control form-control-flush ps-10 bg-light border rounded-pill"
                name="search"
                value=""
                placeholder="Search..."
                data-kt-search-element="input"
              />
            </form>
          </div>

          <div class="d-flex flex-column my-4">
            <app-filter-drop-down
              (filtersApplied)="onFiltersChanged($event)">
            </app-filter-drop-down>
          </div>
        </div>
      </div>
    </div>

    <div class="d-flex h-50px mb-2">
      <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bolder flex-nowrap">
        <li class="nav-item" *ngIf="user?.role === 'broker'">
          <a class="nav-link me-2 pt-0 pb-0 btn btn-active-dark-blue btn-light-primary"
            routerLink="/requests/received"
            routerLinkActive="active">
            Received Requests
            <span class="ms-2 badge badge-light-danger" *ngIf="newRequestsCount > 0">{{ newRequestsCount }}</span>          </a>
        </li>
        <li class="nav-item">
          <a
            class="nav-link me-6 btn btn-active-dark-blue btn-light-primary"
            routerLink="/requests/sent"
            routerLinkActive="active"
          >
            Sent Requests
          </a>
        </li>
      </ul>
    </div>

    <div class="card-body pt-3 pb-0">
      <router-outlet></router-outlet>
    </div>
  </div>
</div>
