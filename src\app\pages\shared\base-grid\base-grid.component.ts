import { AfterViewInit, ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import { Page } from 'src/app/models/page.model';
import { environment } from 'src/environments/environment';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-base-grid',
  templateUrl: './base-grid.component.html',
  styleUrl: './base-grid.component.scss'
})

export class BaseGridComponent implements OnInit, AfterViewInit {

  rows: any[] = [];
  columns: any[] = [];
  filters: any[] = [];
  page: Page = new Page();
  _service: any;
  orderBy: string;
  orderDir: string;
  title: string;
  loading: boolean = false;

  @ViewChild('table') table: any;

  constructor(protected cd: ChangeDetectorRef){

  }

  ngAfterViewInit() {
    return this.reloadTable(this.page);
  }

  ngOnInit() {
    this.page.pageNumber = 0;
    this.page.size = environment.TABLE_LIMIT;
    this.page.orderBy = this.orderBy;
    this.page.orderDir = this.orderDir;
  }

  setService(service: any) {
    this._service = service;
  }

  async reloadTable(pageInfo: any) {
    this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;

    this.loading = true;
    await this._service.getAll(this.page).subscribe(
      (pagedData: any) => {
        console.log(pagedData.data);
        this.rows = Array.isArray(pagedData.data)? pagedData.data : [];
        this.rows = [...this.rows];

        this.page.totalElements = pagedData.count;
        this.page.count = Math.ceil(pagedData.count / this.page.size);

        this.cd.markForCheck();
        this.loading = false;

        this.afterGridLoaded();
        MenuComponent.reinitialization();
      },
      (error: any) => {
        console.log(error);
        this.cd.markForCheck();
        this.loading = false;
        Swal.fire('Failed to load data. please try again later.', '', 'error');
      }
    )
  }

  onPageChange(newPageNumber: number) {
    this.page.pageNumber = newPageNumber;
    this.reloadTable(this.page);
  }

  async sortCallBack(sortInfo: { sorts: { dir: string, prop: string }[], column: {}, prevValue: string, newValue: string}){
    this.page.orderDir = sortInfo.sorts[0].dir;
    this.page.orderBy = sortInfo.sorts[0].prop;
    await this.reloadTable(this.page);
  }

  async filter() {
    await this.reloadTable(this.page);
  }

  afterGridLoaded() {

  }

  capitalizeWords(text: string): string {
    return text.replace(/\b\w/g, char => char.toUpperCase());
  }

    sortData(column: string) {
     if (this.orderBy === column) {
      this.orderDir = this.orderDir === 'asc' ? 'desc' : 'asc';
    } else {
      this.orderBy = column;
      this.orderDir = 'asc';
    }

     this.page.orderBy = this.orderBy;
    this.page.orderDir = this.orderDir;
    this.page.pageNumber = 0;
    this.reloadTable(this.page);
  }

   getSortArrow(column: string): string {
    if (this.orderBy !== column) {
      return '';
    }
    return this.orderDir === 'asc' ? '↑' : '↓';
  }

}
