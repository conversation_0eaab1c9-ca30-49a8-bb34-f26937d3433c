// Localization is based on '@ngx-translate/core';
// Please be familiar with official documentations first => https://github.com/ngx-translate/core

import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject } from 'rxjs';

export interface Locale {
  lang: string;
  data: any;
}

const LOCALIZATION_LOCAL_STORAGE_KEY = 'language';

@Injectable({
  providedIn: 'root',
})
export class TranslationService {
  // Private properties
  private langIds: any = [];
  private currentLanguageSubject = new BehaviorSubject<string>('en');
  public currentLanguage$ = this.currentLanguageSubject.asObservable();

  constructor(private translate: TranslateService) {
    // add new langIds to the list
    this.translate.addLangs(['en', 'ar']);

    // this language will be used as a fallback when a translation isn't found in the current language
    this.translate.setDefaultLang('en');

    // Initialize with stored language or default
    const storedLang = this.getSelectedLanguage();

    // Apply initial direction
    this.applyDirection(storedLang);

    // Set initial language
    this.translate.use(storedLang);
    this.currentLanguageSubject.next(storedLang);
  }

  loadTranslations(...args: Locale[]): void {
    const locales = [...args];

    locales.forEach((locale) => {
      // use setTranslation() with the third argument set to true
      // to append translations instead of replacing them
      this.translate.setTranslation(locale.lang, locale.data, true);
      this.langIds.push(locale.lang);
    });

    // add new languages to the list
    this.translate.addLangs(this.langIds);
    this.translate.use(this.getSelectedLanguage());
  }

  setLanguage(lang: string) {
    if (lang) {
      this.translate.use(this.translate.getDefaultLang());
      this.translate.use(lang);
      localStorage.setItem(LOCALIZATION_LOCAL_STORAGE_KEY, lang);
      this.currentLanguageSubject.next(lang);

      // Apply RTL/LTR direction
      this.applyDirection(lang);
    }
  }

  /**
   * Returns selected language
   */
  getSelectedLanguage(): any {
    return (
      localStorage.getItem(LOCALIZATION_LOCAL_STORAGE_KEY) ||
      this.translate.getDefaultLang()
    );
  }

  /**
   * Apply RTL/LTR direction based on language
   */
  private applyDirection(lang: string) {
    const htmlElement = document.documentElement;
    const bodyElement = document.body;

    if (lang === 'ar') {
      htmlElement.setAttribute('dir', 'rtl');
      htmlElement.setAttribute('lang', 'ar');
      bodyElement.classList.add('rtl');
      bodyElement.classList.remove('ltr');
    } else {
      htmlElement.setAttribute('dir', 'ltr');
      htmlElement.setAttribute('lang', 'en');
      bodyElement.classList.add('ltr');
      bodyElement.classList.remove('rtl');
    }
  }

  /**
   * Check if current language is RTL
   */
  isRTL(): boolean {
    return this.getSelectedLanguage() === 'ar';
  }

  /**
   * Get current language
   */
  getCurrentLanguage(): string {
    return this.getSelectedLanguage();
  }

  /**
   * Toggle between Arabic and English
   */
  toggleLanguage() {
    const currentLang = this.getSelectedLanguage();
    const newLang = currentLang === 'ar' ? 'en' : 'ar';
    this.setLanguage(newLang);
  }
}
