import { Injectable } from '@angular/core';
import { AbstractCrudService } from '../../shared/services/abstract-crud.service';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class UnitsService extends AbstractCrudService {
  apiUrl = `${environment.apiUrl}/unit`;

  getUnitById(id: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/${id}`);
  }

  makeSelectedAvailable(ids: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/update-status-unit-available`, { unitIds: ids });
  }
}
