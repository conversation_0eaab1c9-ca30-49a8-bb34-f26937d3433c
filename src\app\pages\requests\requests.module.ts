import { AssigntoBrokersActionMenuComponent } from './components/assignto-brokers-action-menu/assignto-brokers-action-menu.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RequestsRoutingModule } from './requests-routing.module';
import { SentRequestsComponent } from './components/sent-requests/sent-requests.component';
import { ReceivedRequestsComponent } from './components/received-requests/received-requests.component';
import { RenderRequestComponent } from './components/render-request/render-request.component';
import { RequestOverviewComponent } from './components/render-request/request-overview/request-overview.component';
import { RequestHistoryComponent } from './components/render-request/request-history/request-history.component';
import { RequestRecommendationsComponent } from './components/render-request/request-recommendations/request-recommendations.component';
import { SentRequestsActionsMenuComponent } from './components/sent-requests-actions-menu/sent-requests-actions-menu.component';

import { BrokerModule } from '../broker/broker.module';
import { NgApexchartsModule } from 'ng-apexcharts';
import { SharedModule } from 'src/app/_metronic/shared/shared.module';
import { RouterModule } from '@angular/router';
import { RequestsComponent } from './requests.component';
import { AssignToBrokersComponent } from './components/assign-to-brokers/assign-to-brokers.component';
import { FormsModule } from '@angular/forms';
import { PaginationComponent } from '../../pagination/pagination.component';
import { RequestRepliesComponent } from './components/render-request/request-replies/request-replies.component';
import { NgbModalModule } from '@ng-bootstrap/ng-bootstrap';
import { FilterDropDownComponent } from './components/filter-drop-down/filter-drop-down.component';
import { ReactiveFormsModule } from '@angular/forms';
import { RatingModule } from '../shared/rating/rating.module';

@NgModule({
  declarations: [
    RequestsComponent,
    SentRequestsComponent,
    ReceivedRequestsComponent,
    RenderRequestComponent,
    RequestOverviewComponent,
    RequestHistoryComponent,
    RequestRepliesComponent,
    RequestRecommendationsComponent,
    AssignToBrokersComponent,
    AssigntoBrokersActionMenuComponent,
    FilterDropDownComponent,
    SentRequestsActionsMenuComponent,
  ],
  imports: [
    CommonModule,
    RequestsRoutingModule,
    BrokerModule,
    NgApexchartsModule,
    SharedModule,
    RouterModule,
    FormsModule,
    NgbModalModule,
    PaginationComponent,
    ReactiveFormsModule,
    RatingModule,
  ],
  exports: [
    SentRequestsComponent,
    ReceivedRequestsComponent,
    RenderRequestComponent,
    RequestOverviewComponent,
    RequestHistoryComponent,
    RequestRepliesComponent,
    AssigntoBrokersActionMenuComponent,
    RequestRecommendationsComponent,
    FilterDropDownComponent,
  ],
})
export class RequestsModule {}
