import { Injectable } from '@angular/core';
import { BaseConfigService, InputConfig, StepperConfiguration } from './base-config.service';
import { SellConfigService } from './inside-compound/sell-config.service';
import { PurchasingConfigService } from './inside-compound/purchasing-config.service';
import { RentalConfigService } from './inside-compound/rental-config.service';
import { RentalOutsideCompoundConfigService } from './outside-compound/rental-outside-compound-config.service';
import { SellOutsideCompoundConfigService } from './outside-compound/sell-outside-compound-config.service';
import { PurchaseOutsideCompoundConfigService } from './outside-compound/purchase-outside-compound-config.service';

@Injectable({
  providedIn: 'root'
})
export class ConfigFactoryService extends BaseConfigService {

  constructor(

    private sellConfigService: SellConfigService,
    private purchasingConfigService: PurchasingConfigService,
    private rentalConfigService: RentalConfigService,
    private rentalOutsideCompoundConfigService: RentalOutsideCompoundConfigService,
    private sellOutsideCompoundConfigService: SellOutsideCompoundConfigService,
    private purchaseOutsideCompoundConfigService: PurchaseOutsideCompoundConfigService
  ) {
    super();
  }

  /**
   * Get input configurations from all services
   */
  getInputConfigs(stepperModal: any): StepperConfiguration[] {
    return [
      ...this.sellConfigService.getInputConfigs(stepperModal),
      ...this.purchasingConfigService.getInputConfigs(stepperModal),
      ...this.rentalConfigService.getInputConfigs(stepperModal),
      ...this.rentalOutsideCompoundConfigService.getInputConfigs(stepperModal),
      ...this.sellOutsideCompoundConfigService.getInputConfigs(stepperModal),
      ...this.purchaseOutsideCompoundConfigService.getInputConfigs(stepperModal),
    ];
  }

    /**
   * Get inputs for a specific configuration key and step
   */
  getInputsForKey(key: string, step: number, stepperModal: any): InputConfig[] {
    // Check sell outside compound configurations first (more specific)
    if (key.includes('purchase_sell_outside_compound_sell_')) {
      return this.sellOutsideCompoundConfigService.getInputsForKey(key, step, stepperModal);
    }

    // Check purchase outside compound configurations (more specific)
    if (key.includes('purchase_sell_outside_compound_purchasing_')) {
      return this.purchaseOutsideCompoundConfigService.getInputsForKey(key, step, stepperModal);
    }

    // Check sell configurations (inside compound)
    if (key.includes('_sell_')) {
      return this.sellConfigService.getInputsForKey(key, step, stepperModal);
    }

    // Check purchase configurations
    if (key.includes('_purchasing_')) {
      return this.purchasingConfigService.getInputsForKey(key, step, stepperModal);
    }

    // Check rental outside compound configurations (more specific)
    if (key.includes('rentals_outside_compound_rent_out_') || key.includes('rentals_outside_compound_rent_in_')) {
      return this.rentalOutsideCompoundConfigService.getInputsForKey(key, step, stepperModal);
    }

    // Check rental configurations (general)
    if (key.includes('_rent_')) {
      return this.rentalConfigService.getInputsForKey(key, step, stepperModal);
    }

    // Default fallback
    return this.getDefaultInputsForStep(step);
  }

  /**
   * Check if a configuration key exists in any service
   */
  hasConfiguration(key: string, stepperModal: any): boolean {
      return this.sellConfigService.hasConfiguration(key, stepperModal) ||
            this.purchasingConfigService.hasConfiguration(key, stepperModal) ||
            this.rentalConfigService.hasConfiguration(key, stepperModal)  ||
            this.rentalOutsideCompoundConfigService.hasConfiguration(key, stepperModal) ||
            this.sellOutsideCompoundConfigService.hasConfiguration(key, stepperModal) ||
            this.purchaseOutsideCompoundConfigService.hasConfiguration(key, stepperModal);
  }

  /**
   * Get all available configuration keys from all services
   */
  getAllAvailableConfigKeys(stepperModal: any): string[] {
    const allConfigs = this.getInputConfigs(stepperModal);
    return allConfigs.map(config => config.key);
  }

  /**
   * Get configuration type based on key
   */
  getConfigurationType(key: string): 'sell' | 'purchase' | 'rental' | 'rentals_outside_compound' | 'sell_outside_compound' | 'purchase_outside_compound' | 'unknown' {
    if (key.includes('purchase_sell_outside_compound_sell_')) return 'sell_outside_compound';
    if (key.includes('purchase_sell_outside_compound_purchasing_')) return 'purchase_outside_compound';
    if (key.includes('_sell_')) return 'sell';
    if (key.includes('_purchasing_')) return 'purchase';
    if (key.includes('rentals_outside_compound_rent_out_') || key.includes('rentals_outside_compound_rent_in_')) return 'rentals_outside_compound';
    if (key.includes('_rent_')) return 'rental';
    return 'unknown';
  }
}
