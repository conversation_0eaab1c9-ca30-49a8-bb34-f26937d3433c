<div class="card">
  <div class="card-header border-0 pt-6">
    <!--begin::Card title-->
    <div class="card-title">
      <!--begin::Search-->
      <div class="d-flex align-items-center position-relative my-1">
        <app-keenicon name="magnifier" class="fs-3 position-absolute ms-5"></app-keenicon>
        <input type="text" data-action="filter" class="form-control form-control-solid w-250px ps-12" placeholder="Search Users">
      </div>
      <!--end::Search-->
    </div>
    <!--begin::Card title-->

    <!--begin::Card toolbar-->
    <div class="card-toolbar">
      <!--begin::Toolbar-->
      <div class="d-flex justify-content-end">
        <!--begin::Add user-->
        <button type="button" class="btn btn-primary" data-action="create">
          Add User
        </button>
        <!--end::Add user-->
      </div>
      <!--end::Toolbar-->
    </div>
    <!--end::Card toolbar-->
  </div>

  <div class="card-body pt-0">
    <app-crud [datatableConfig]="datatableConfig" route="/apps/users" (deleteEvent)="delete($event)" (editEvent)="edit($event)" (createEvent)="create()" [reload]="reloadEvent" [modal]="formModal"></app-crud>
  </div>
</div>

<swal #noticeSwal [swalOptions]="swalOptions">
</swal>

<ng-template #formModal let-modal>
  <form #myForm="ngForm" (ngSubmit)="onSubmit($event, myForm)">
    <div class="modal-header">
      <h4 class="modal-title" id="modal-basic-title">User Details</h4>
      <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modal-body">
      <div class="d-flex flex-column scroll-y me-n7 pe-7 mw-650px" id="kt_modal_update_customer_scroll" data-kt-scroll="true" data-kt-scroll-activate="{default: false, lg: true}" data-kt-scroll-max-height="auto" data-kt-scroll-dependencies="#kt_modal_update_customer_header" data-kt-scroll-wrappers="#kt_modal_update_customer_scroll"
        data-kt-scroll-offset="300px">
        <!--begin::Notice-->
        <div class="notice d-flex bg-light-primary rounded border-primary border border-dashed mb-9 p-6">
          <!--begin::Icon-->
          <app-keenicon name="information" class="fs-2tx text-primary me-4"></app-keenicon>
          <!--end::Icon-->
          <!--begin::Wrapper-->
          <div class="d-flex flex-stack flex-grow-1">
            <!--begin::Content-->
            <div class="fw-semibold">
              <div class="fs-6 text-gray-700">Updating customer details will receive a privacy audit. For more info,
                please read our
                <a href="#">Privacy Policy</a>
              </div>
            </div>
            <!--end::Content-->
          </div>
          <!--end::Wrapper-->
        </div>
        <!--end::Notice-->

        <!--begin::User toggle-->
        <div class="fw-bold fs-3 rotate collapsible mb-7" data-bs-toggle="collapse" role="button" (click)="collapse1.toggle()" [attr.aria-expanded]="!isCollapsed1" aria-controls="kt_modal_update_customer_user_info">User Information
          <span class="ms-2 rotate-180">
            <i class="ki-duotone ki-down fs-3" [ngClass]="{ 'ki-up': !isCollapsed1 }"></i>
          </span>
        </div>
        <!--end::User toggle-->
        <!--begin::User form-->
        <div class="collapse show" #collapse1="ngbCollapse" [(ngbCollapse)]="isCollapsed1">
          <!--begin::Input group-->
          <div class="fv-row mb-7">
            <!--begin::Label-->
            <label class="required fw-semibold fs-6 mb-2">Name</label>
            <!--end::Label-->
            <!--begin::Input-->
            <input type="text" class="form-control form-control-solid" name="name" [(ngModel)]="userModel.name" #name="ngModel" required minlength="4" />
            <div *ngIf="myForm.submitted && myForm.invalid" class="fv-plugins-message-container fv-plugins-message-container--enabled invalid-feedback">
              <div *ngIf="name.errors?.['required']">Name is required</div>
              <div *ngIf="name.errors?.['minlength']">Name must be at least 4 characters long</div>
            </div>
            <!--end::Input-->
          </div>
          <!--end::Input group-->
          <!--begin::Input group-->
          <div class="fv-row mb-7">
            <!--begin::Label-->
            <label class="required fw-semibold fs-6 mb-2">
              <span>Email</span>
              <span class="ms-1" placement="end" ngbTooltip="Email address must be active">
                <app-keenicon name="information" class="fs-7"></app-keenicon>
              </span>
            </label>
            <!--end::Label-->
            <!--begin::Input-->
            <input type="email" class="form-control form-control-solid" name="email" [(ngModel)]="userModel.email" #email="ngModel" required pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$" />
            <div *ngIf="myForm.submitted && myForm.invalid" class="fv-plugins-message-container fv-plugins-message-container--enabled invalid-feedback">
              <div *ngIf="email.errors?.['required']">Email is required</div>
              <div *ngIf="email.errors?.['pattern']">Email is not valid</div>
            </div>
            <!--end::Input-->
          </div>
          <!--end::Input group-->

          <div class="mb-7">
            <label class="required fw-semibold fs-6 mb-5">Role</label>

            <div *ngFor="let role of (roles$ | async)?.data; let last = last">
              <div class="d-flex fv-row">
                <div class="form-check form-check-custom form-check-solid">
                  <input class="form-check-input me-3" id="kt_modal_update_role_option_{{ role.id }}" name="role" #role="ngModel" [(ngModel)]="userModel.role" type="radio" [value]="role.name" [checked]="last" />
                  <label class="form-check-label" for="kt_modal_update_role_option_{{ role.id }}">
                    <div class="fw-bold text-gray-800">
                      {{ role.name | titlecase }}
                    </div>
                    <div class="text-gray-600">
                      {{ role.name }}
                    </div>
                  </label>
                </div>
              </div>
              <div *ngIf="!last" class="separator separator-dashed my-5"></div>
            </div>

          </div>

        </div>
        <!--end::User form-->
      </div>
    </div>
    <div class="modal-footer flex-center">
      <!--begin::Button-->
      <button type="reset" id="kt_modal_update_customer_cancel" class="btn btn-light me-3" (click)="modal.dismiss('cancel')">
        Discard
      </button>
      <!--end::Button-->

      <!--begin::Button-->
      <button type="submit" id="kt_modal_update_customer_submit" class="btn btn-primary" [attr.data-kt-indicator]="isLoading ? 'on' : 'off'">
        <span class="indicator-label">
          Submit
        </span>
        <span class="indicator-progress">
          Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
        </span>
      </button>
      <!--end::Button-->
    </div>
  </form>
</ng-template>