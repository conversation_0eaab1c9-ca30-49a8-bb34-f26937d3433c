import { ChangeDetectorRef, Component, EventEmitter, Output, OnInit } from '@angular/core';
import { PropertyService } from 'src/app/pages/broker/services/property.service';

@Component({
  selector: 'app-model-filter',
  templateUrl: './model-filter.component.html',
  styleUrl: './model-filter.component.scss'
})
export class ModelFilterComponent implements OnInit {

  unitTypes: { key: string; value: string }[] = [];

  @Output() filtersApplied = new EventEmitter<any>();

  filter = {
    noOfRooms: '',
    noOfBathrooms: '',
  };

  constructor( private propertyService: PropertyService, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {}

  apply() {
    this.filtersApplied.emit(this.filter);
  }


}
