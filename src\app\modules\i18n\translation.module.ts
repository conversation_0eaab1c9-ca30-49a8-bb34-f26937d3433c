import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { TranslationService } from './translation.service';

// Import translation files
import { locale as enLang } from './vocabs/en';
import { locale as arLang } from './vocabs/ar';

@NgModule({
  imports: [CommonModule, TranslateModule],
  exports: [TranslateModule],
  providers: [TranslationService],
})
export class TranslationModule {
  constructor(private translationService: TranslationService) {
    // Load translations when module is initialized
    this.translationService.loadTranslations(enLang, arLang);
  }
}
