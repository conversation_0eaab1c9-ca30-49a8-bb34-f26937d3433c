import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { DevelopersService } from '../services/developer.service';
import { getCSSVariableValue } from 'src/app/_metronic/kt/_utils';

@Component({
  selector: 'app-developer-dashboard',
  templateUrl: './developer-dashboard.component.html',
  styleUrl: './developer-dashboard.component.scss',
})
export class DeveloperDashboardComponent implements OnInit {

  newRequests: any[] = [];
  newRequestsCount = 0;
  unitStats: any = {};
  contractStats: any = {};
  projectStatsData: any = {
    apartments_count: 0,
    buildings_count: 0,
    villas_count: 0,
    duplex_count: 0,
    administrative_units_count: 0,
    commercial_units_count: 0,
  };
  numberOfProjects: number = 0;
  developerId: number;

  // Top 5 Models data
  top5ModelsOverAll: any[] = [];
  filteredTop5Models: any[] = [];

  // Date filters
  dateFrom: string = '';
  dateTo: string = '';

  orderBy: string = 'id';
  orderDir: 'asc' | 'desc' = 'desc';

  constructor(
    private developerService: DevelopersService,
    private cd: ChangeDetectorRef
  ) {}

  ngOnInit() {
    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;
    this.developerId = user?.developerId;
    this.loadStatistics();
    this.loadPieChartStatistics();
  }

  loadStatistics() {
    this.developerService.getDeveloperStatistics(this.developerId).subscribe({
      next: (response) => {
        console.log('Statistics response:', response.data);
        console.log('Statistics count:', response.count);
        this.newRequests = response.data;
        this.newRequestsCount = response.count;

        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading statistics:', error);
        this.newRequests = [];
      },
    });
  }

  loadPieChartStatistics() {
    this.developerService
      .getDeveloperPieChartStatistics(this.developerId)
      .subscribe({
        next: (response) => {
          if (response.data) {
            const data = response.data;
            this.unitStats = data.statistics.unitStats;
            this.contractStats = data.statistics.contractRequestStats;
            this.projectStatsData = data.statistics.projectStats;
            this.numberOfProjects = data.statistics.numberOfProjects;
            this.top5ModelsOverAll = data.top5ModelsOverAll;
            this.filteredTop5Models = [...this.top5ModelsOverAll];

            console.log('Statistics data:', data.statistics);
            console.log('unitStats:', this.unitStats);
            console.log('contractStats:', this.contractStats);
            console.log('projectStatsData:', this.projectStatsData);
            console.log('numberOfProjects:', this.numberOfProjects);
            console.log('top5ModelsOverAll:', this.top5ModelsOverAll);

            this.cd.detectChanges();
          }
        },
        error: (error) => {
          console.error('Error loading pie chart statistics:', error);
        },
      });
  }

  sortData(column: string) {
    this.orderDir =
      this.orderBy === column
        ? this.orderDir === 'asc'
          ? 'desc'
          : 'asc'
        : 'asc';
    this.orderBy = column;

    this.newRequests.sort((a, b) => {
      const valA = column.includes('.')
        ? column.split('.').reduce((o, k) => o?.[k], a)
        : a[column];
      const valB = column.includes('.')
        ? column.split('.').reduce((o, k) => o?.[k], b)
        : b[column];
      return this.orderDir === 'asc'
        ? valA > valB
          ? 1
          : -1
        : valA < valB
        ? 1
        : -1;
    });
  }

  getSortArrow(column: string): string {
    return this.orderBy === column ? (this.orderDir === 'asc' ? '↑' : '↓') : '';
  }

  onDateFilterChange() {
    this.applyDateFilters();
  }

  applyDateFilters() {
    if (!this.dateFrom && !this.dateTo) {
      this.filteredTop5Models = [...this.top5ModelsOverAll];
      return;
    }

    this.filteredTop5Models = this.top5ModelsOverAll.filter((model) => {
      const modelDate = new Date(model?.model?.modelDate);

      if (this.dateFrom && this.dateTo) {
        const fromDate = new Date(this.dateFrom);
        const toDate = new Date(this.dateTo);
        return modelDate >= fromDate && modelDate <= toDate;
      } else if (this.dateFrom) {
        const fromDate = new Date(this.dateFrom);
        return modelDate >= fromDate;
      } else if (this.dateTo) {
        const toDate = new Date(this.dateTo);
        return modelDate <= toDate;
      }

      return true;
    });
  }

  clearDateFilters() {
    this.dateFrom = '';
    this.dateTo = '';
    this.filteredTop5Models = [...this.top5ModelsOverAll];
  }
}
