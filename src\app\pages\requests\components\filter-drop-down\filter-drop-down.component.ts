import { Component, Input, Output, EventEmitter, HostListener, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-filter-drop-down',
  templateUrl: './filter-drop-down.component.html',
  styleUrls: ['./filter-drop-down.component.scss']
})

export class FilterDropDownComponent implements OnInit {

  @Input() placeholder: string = 'Select an option';
  @Output() filtersApplied = new EventEmitter<any>();

  isOpen: boolean = false;
  selectedMainOption: string = '';
  selectedChildOption: string = '';
  filteredOptions: string[] = [];
  activeChildDropdown: string | null = null;
  form: FormGroup;
  options = {
    specializationScope: [
      'purchase_sell_outside_compound',
      // 'purchase_sell_inside_compound',
      'primary_inside_compound',
      'resale_inside_compound',
      'rentals_outside_compound',
      'rentals_inside_compound'
    ],
    status: ['new', 'in_processing', 'finished'],
    type: ['sell', 'purchasing', 'rent_in', 'rent_out'],
  };

  constructor(private fb: FormBuilder) {
    this.form = this.fb.group({
      specializationScope: [''],
      status: [''],
      type: ['']
    });
  }

  ngOnInit() {}

  toggleDropdown(event: Event): void {
    event.stopPropagation();
    this.isOpen = !this.isOpen;
    if (!this.isOpen) {
      this.activeChildDropdown = null;
    }
  }

  onSubmit() {
    if (this.form.valid) {
      // Emit the form values to the parent component
      this.filtersApplied.emit(this.form.value);
      this.closeDropdown();
    }
  }

  closeDropdown() {
    this.isOpen = false;
  }
}
