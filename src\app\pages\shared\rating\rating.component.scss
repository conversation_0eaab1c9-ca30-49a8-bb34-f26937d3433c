.rating-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 30px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  font-family: 'Arial', sans-serif;
}

.rating-title {
  text-align: center;
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 30px;
  direction: rtl;
}

.rating-form {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.rating-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
  direction: rtl;

  &:last-of-type {
    border-bottom: none;
  }
}

.rating-label {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  min-width: 150px;
  text-align: right;
}

.stars-container {
  display: flex;
  gap: 8px;
  direction: ltr;
}

.star {
  font-size: 32px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;

  &:hover {
    transform: scale(1.1);
  }

  &.filled {
    color: #ffd700;
    filter: brightness(1.2);
  }

  &:not(.filled) {
    filter: grayscale(100%) brightness(0.7);
    opacity: 0.5;
  }
}

.submit-button {
  background: linear-gradient(135deg, #4285f4, #1a73e8);
  color: white;
  border: none;
  padding: 16px 40px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 20px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.3);

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #1a73e8, #1557b0);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(66, 133, 244, 0.4);
  }

  &:disabled {
    background: #cccccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }
}

// Responsive design
@media (max-width: 768px) {
  .rating-container {
    padding: 20px;
    margin: 10px;
  }

  .rating-row {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .rating-label {
    min-width: auto;
    text-align: center;
  }

  .star {
    font-size: 28px;
  }

  .submit-button {
    padding: 14px 30px;
    font-size: 16px;
  }
}
