/* نفس الديزاين الأصلي بالضبط */
.language-toggle {
  position: relative;
  display: inline-block;
  border-radius: 20px;
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(40, 167, 69, 0.1));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 2px;

  .language-btn {
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.9));
    color: #333;
    padding: 6px 12px;
    border-radius: 16px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 100px;
    justify-content: space-between;
    font-size: 0.85rem;

    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(248, 249, 250, 0.95));
      border-color: rgba(40, 167, 69, 0.5);
      color: #333;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
      color: white;
    }

    .fa-chevron-down {
      transition: transform 0.3s ease;
      color: #28a745;

      &.rotated {
        transform: rotate(180deg);
      }
    }
  }

  .language-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(222, 226, 230, 0.8);
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    margin-top: 8px;

    &.show {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .language-option {
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      align-items: center;
      color: #333;
      transition: all 0.2s ease;
      border: none;
      background: none;
      width: 100%;
      text-align: left;

      &:hover {
        background-color: rgba(40, 167, 69, 0.1);
        color: #333;
      }

      &.active {
        background-color: rgba(40, 167, 69, 0.15);
        color: #333;
        font-weight: 500;
      }

      .fa-check {
        color: #28a745;
      }

      &:first-child {
        border-radius: 20px 20px 0 0;
      }

      &:last-child {
        border-radius: 0 0 20px 20px;
      }

      i {
        width: 16px;
        text-align: center;
      }
    }
  }
}

// RTL Support
:host-context(html[dir="rtl"]) .language-toggle,
:host-context(html[lang="ar"]) .language-toggle,
.rtl .language-toggle {
  .language-dropdown {
    left: auto;
    right: 0;
  }

  .language-btn {
    flex-direction: row-reverse;
    min-width: 140px; // زيادة العرض للعربية
    padding: 8px 20px; // زيادة الـ padding

    .language-text {
      font-size: 0.9rem;
      font-weight: 600;
    }
  }

  .language-option {
    text-align: right;
    flex-direction: row-reverse;
    padding: 12px 20px; // زيادة الـ padding

    span {
      font-size: 0.9rem;
      font-weight: 500;
    }
  }
}

// Mobile responsive
@media (max-width: 768px) {
  .language-toggle {
    .language-btn {
      min-width: 80px;
      padding: 4px 8px;
      font-size: 0.75rem;
      gap: 4px;
    }

    .language-dropdown {
      min-width: 120px;
    }
  }

  // RTL Mobile fixes
  :host-context(html[dir="rtl"]) .language-toggle,
  :host-context(html[lang="ar"]) .language-toggle {
    .language-btn {
      min-width: 100px; // عرض أصغر للعربية في الموبايل
      padding: 6px 12px;

      .language-text {
        font-size: 0.8rem;
      }
    }

    .language-dropdown {
      min-width: 140px; // عرض أصغر للقائمة المنسدلة
    }
  }
}
