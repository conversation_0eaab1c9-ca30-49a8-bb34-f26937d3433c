<!--begin::Chat-->
<div class="app-navbar-item" [ngClass]="itemClass">
  <div [ngClass]="btnClass" class="position-relative" id="kt_drawer_chat_toggle">
    <app-keenicon name="message-text-2" class="fs-2"></app-keenicon>
    <span
      class="bullet bullet-dot bg-success h-6px w-6px position-absolute translate-middle top-0 start-50 animation-blink">
    </span>
  </div>
</div>
<!--end::Chat-->

<!--begin::Notifications-->
<div class="app-navbar-item" [ngClass]="itemClass">
  <!--begin::Menu- wrapper-->
  <div [ngClass]="btnClass" data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent"
    data-kt-menu-placement="bottom-end">
    <app-keenicon name="notification-on" class="fs-2"></app-keenicon>
  </div>
  <app-notifications-inner></app-notifications-inner>
  <!--end::Menu wrapper-->
</div>
<!--end::Notifications-->

<!--begin::Theme mode-->
<div class="app-navbar-item" [ngClass]="itemClass">
  <app-theme-mode-switcher [toggleBtnClass]="btnClass" toggleBtnClass="{`btn-active-light-primary btn-custom ${toolbarButtonHeightClass}`}"></app-theme-mode-switcher>
</div>
<!--end::Theme mode-->

<!--begin::User menu-->
<div class="app-navbar-item" [ngClass]="itemClass">
  <!--begin::Menu wrapper-->
  <div class="cursor-pointer symbol" [ngClass]="userAvatarClass" data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end">
    <img src="./assets/media/avatars/300-3.jpg" alt="User profile image..."/>
  </div>
  <app-user-inner data-kt-menu='true'></app-user-inner>
  <!--end::Menu wrapper-->
</div>
<!--end::User menu-->

<!--begin::Header menu toggle-->
<!-- <ng-container *ngIf="appHeaderDefaulMenuDisplay">
  <div class="app-navbar-item d-lg-none ms-2 me-n3" title="Show header menu">
    <div class="btn btn-icon btn-active-color-primary w-35px h-35px" id="kt_app_header_menu_toggle">
      <app-keenicon name="element-4" class="fs-1" [ngClass]="btnIconClass"></app-keenicon>
    </div>
  </div>
</ng-container> -->
<!--end::Header menu toggle-->
