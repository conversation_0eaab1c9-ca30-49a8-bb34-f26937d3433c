<!-- begin::Row -->
<div class="row g-5 g-xl-8">
  <div class="col-xl-4">
    <app-stats-widget1
      class="card bgi-no-repeat card-xl-stretch mb-xl-8"
      title="Meeting Schedule"
      time="3:30PM - 4:20PM"
      description="Create a headline that is informative<br/>and will capture readers"
      [ngStyle]="{
        'background-position': 'right top',
        'background-size': '30% auto',
        'background-image': 'url(./assets/media/svg/shapes/abstract-4.svg)'
      }"
    ></app-stats-widget1>
  </div>

  <div class="col-xl-4">
    <app-stats-widget1
      class="card bgi-no-repeat card-xl-stretch mb-xl-8"
      title="Meeting Schedule"
      time="03 May 2020"
      description="Great blog posts don’t just happen Even the best bloggers need it"
      [ngStyle]="{
        'background-position': 'right top',
        'background-size': '30% auto',
        'background-image': 'url(./assets/media/svg/shapes/abstract-4.svg)'
      }"
    ></app-stats-widget1>
  </div>

  <div class="col-xl-4">
    <app-stats-widget1
      class="card bgi-no-repeat card-xl-stretch mb-5 mb-xl-8"
      title="UI Conference"
      time="10AM Jan, 2021"
      description="AirWays - A Front-end solution for airlines build with ReactJS"
      [ngStyle]="{
        'background-position': 'right top',
        'background-size': '30% auto',
        'background-image': 'url(./assets/media/svg/shapes/abstract-4.svg)'
      }"
    ></app-stats-widget1>
  </div>
</div>
<!-- end::Row -->

<!-- begin::Row -->
<div class="row g-5 g-xl-8">
  <div class="col-xl-4">
    <app-stats-widget2
      class="card card-xl-stretch mb-xl-8"
      avatar="./assets/media/svg/avatars/029-boy-11.svg"
      title="Arthur Goldstain"
      description="System & Software Architect"
    ></app-stats-widget2>
  </div>

  <div class="col-xl-4">
    <app-stats-widget2
      class="card card-xl-stretch mb-xl-8"
      avatar="./assets/media/svg/avatars/014-girl-7.svg"
      title="Lisa Bold"
      description="Marketing & Fanance Manager"
    ></app-stats-widget2>
  </div>

  <div class="col-xl-4">
    <app-stats-widget2
      class="card card-xl-stretch mb-5 mb-xl-8"
      avatar="./assets/media/svg/avatars/004-boy-1.svg"
      title="Nick Stone"
      description="Customer Support Team"
    ></app-stats-widget2>
  </div>
</div>
<!-- end::Row -->

<!-- begin::Row -->
<div class="row g-5 g-xl-8">
  <div class="col-xl-4">
    <app-stats-widget3
      class="card card-xl-stretch mb-xl-8"
      color="success"
      change="+100"
      title="Weekly Sales"
      description="Your Weekly Sales Chart"
    ></app-stats-widget3>
  </div>

  <div class="col-xl-4">
    <app-stats-widget3
      class="card card-xl-stretch mb-xl-8"
      color="danger"
      change="-260"
      title="Authors Progress"
      description="Marketplace Authors Chart"
    ></app-stats-widget3>
  </div>

  <div class="col-xl-4">
    <app-stats-widget3
      class="card card-xl-stretch mb-5 mb-xl-8"
      color="primary"
      change="+180"
      title="Sales Progress"
      description="Marketplace Sales Chart"
    ></app-stats-widget3>
  </div>
</div>
<!-- end::Row -->

<!-- begin::Row -->
<div class="row g-5 g-xl-8">
  <div class="col-xl-4">
    <app-stats-widget4
      svgIcon="./assets/media/icons/duotune/ecommerce/basket"
      class="card card-xl-stretch mb-xl-8"
      color="info"
      change="+256"
      description="Sales Change"
    ></app-stats-widget4>
  </div>

  <div class="col-xl-4">
    <app-stats-widget4
      svgIcon="./assets/media/icons/duotune/general/element-11"
      class="card card-xl-stretch mb-xl-8"
      color="success"
      change="750$"
      description="Weekly Income"
    ></app-stats-widget4>
  </div>

  <div class="col-xl-4">
    <app-stats-widget4
      svgIcon="./assets/media/icons/duotune/general/element-11"
      class="card card-xl-stretch mb-5 mb-xl-8"
      color="primary"
      change="+6.6K"
      description="New Users"
    ></app-stats-widget4>
  </div>
</div>
<!-- end::Row -->

<!-- begin::Row -->
<div class="row g-5 g-xl-8">
  <div class="col-xl-4">
    <app-stats-widget5
      svgIcon="./assets/media/icons/duotune/ecommerce/basket"
      class="card bg-danger hoverable card-xl-stretch mb-xl-8"
      color="danger"
      iconColor="white"
      title="Shopping Cart"
      description="Lands, Houses, Ranchos, Farms"
    ></app-stats-widget5>
  </div>

  <div class="col-xl-4">
    <app-stats-widget5
      svgIcon="./assets/media/icons/duotune/ecommerce/cheque"
      class="card bg-primary hoverable card-xl-stretch mb-xl-8"
      color="primary"
      iconColor="white"
      title="Appartments"
      description="Flats, Shared Rooms, Duplex"
    ></app-stats-widget5>
  </div>

  <div class="col-xl-4">
    <app-stats-widget5
      svgIcon="./assets/media/icons/duotune/graphs/chart-simple-3"
      class="card bg-success hoverable card-xl-stretch mb-5 mb-xl-8"
      color="success"
      iconColor="white"
      title="Sales Stats"
      description="50% Increased for FY20"
    ></app-stats-widget5>
  </div>
</div>
<!-- end::Row -->

<!-- begin::Row -->
<div class="row g-5 g-xl-8">
  <div class="col-xl-3">
    <app-stats-widget5
      svgIcon="./assets/media/icons/duotune/general/chart-simple"
      class="card bg-body hoverable card-xl-stretch mb-xl-8"
      color="white"
      iconColor="primary"
      title="500M$"
      description="SAP UI Progress"
    ></app-stats-widget5>
  </div>

  <div class="col-xl-3">
    <app-stats-widget5
      svgIcon="./assets/media/icons/duotune/ecommerce/cheque"
      class="card bg-dark hoverable card-xl-stretch mb-xl-8"
      color="dark"
      iconColor="white"
      title="+3000"
      description="New Customers"
    ></app-stats-widget5>
  </div>

  <div class="col-xl-3">
    <app-stats-widget5
      svgIcon="./assets/media/icons/duotune/finance/briefcase"
      class="card bg-warning hoverable card-xl-stretch mb-xl-8"
      color="warning"
      iconColor="white"
      title="$50,000"
      description="Milestone Reached"
    ></app-stats-widget5>
  </div>

  <div class="col-xl-3">
    <app-stats-widget5
      svgIcon="./assets/media/icons/duotune/graphs/chart-pie-simple"
      class="card bg-info hoverable card-xl-stretch mb-5 mb-xl-8"
      color="info"
      iconColor="white"
      title="$50,000"
      description="Milestone Reached"
    ></app-stats-widget5>
  </div>
</div>
<!-- end::Row -->

<!-- begin::Row -->
<div class="row g-5 g-xl-8">
  <div class="col-xl-4">
    <app-stats-widget6
      class="card bg-light-success card-xl-stretch mb-xl-8"
      color="success"
      title="Avarage"
      description="Project Progress"
      [progress]="'50%'"
    ></app-stats-widget6>
  </div>

  <div class="col-xl-4">
    <app-stats-widget6
      class="card bg-light-warning card-xl-stretch mb-xl-8"
      color="warning"
      title="48k Goal"
      description="Company Finance"
      progress="15%"
    ></app-stats-widget6>
  </div>

  <div class="col-xl-4">
    <app-stats-widget6
      class="card bg-light-primary card-xl-stretch mb-xl-8"
      color="primary"
      title="400k Impressions"
      description="Marketing Analysis"
      progress="76%"
    ></app-stats-widget6>
  </div>
</div>
<!-- end::Row -->
