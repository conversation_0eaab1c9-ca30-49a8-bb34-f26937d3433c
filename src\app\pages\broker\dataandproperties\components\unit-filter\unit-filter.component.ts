import { ChangeDetectorRef, Component, EventEmitter, Output, Input, OnInit } from '@angular/core';
import { PropertyService } from 'src/app/pages/broker/services/property.service';

@Component({
  selector: 'app-unit-filter',
  templateUrl: './unit-filter.component.html',
  styleUrl: './unit-filter.component.scss'
})
export class UnitFilterComponent implements OnInit {

  unitTypes: { key: string; value: string }[] = [];
  areas: any[] = [];

  @Input() brokerId: number | null = null;
  @Output() filtersApplied = new EventEmitter<any>();

  filter = {
    finishingType: '',
    status: '',
    unitType: '',
    compoundType: '',
    unitArea:'',
    area:'',
    view:'',
    price:'',
  };

  constructor(private propertyService: PropertyService, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.loadUnitTypes();
    this.loadAreas();
  }

  finishingTypes: { key: string; value: string }[] = [
    { key: 'On Brick', value: 'on_brick' },
    { key: 'Semi Finished', value: 'semi_finished' },
    { key: 'Company Finished', value: 'company_finished' },
    { key: 'Super Lux', value: 'super_lux' },
    { key: 'Ultra Super Lux', value: 'ultra_super_lux' },
  ];

  status: { key: string; value: string }[] = [
    { key: 'NEW', value: 'new' },
    { key: 'AVAILABLE', value: 'available' },
    { key: 'RESERVED', value: 'reserved' },
    { key: 'SOLD', value: 'sold' },
  ];

  compoundTypes: { key: string; value: string }[] = [
    { key: 'Outside Compound', value: 'outside_compound' },
    { key: 'Inside Compound', value: 'inside_compound' },
    { key: 'Village', value: 'village' },
  ];


  views: { key: string; value: string }[] = [
    { key: 'WATER VIEW', value: 'water_view' },
    { key: 'GARDENS AND LANDSCAPE', value: 'gardens_and_landscape' },
    { key: 'STREET', value: 'street' },
    { key: 'ENTERTAINMENT AREA', value: 'entertainment_area' },
    { key: 'GARDEN', value: 'garden' },
    { key: 'MAIN STREET', value: 'main_street' },
    { key: 'SQUARE', value: 'square' },
    { key: 'SIDE STREET', value: 'side_street' },
    { key: 'REAR VIEW', value: 'rear_view' },
  ];

  apply() {
    const filtersWithBrokerId = {
      ...this.filter,
      brokerId: this.brokerId
    };
    console.log('Unit filter applying:', filtersWithBrokerId);
    this.filtersApplied.emit(filtersWithBrokerId);
  }

  reset() {
    this.filter = {
      finishingType: '',
      status: '',
      unitType: '',
      compoundType: '',
      unitArea:'',
      area:'',
      view:'',
      price:'',
    };

    const resetFiltersWithBrokerId = {
      ...this.filter,
      brokerId: this.brokerId
    };
    console.log('Unit filter resetting:', resetFiltersWithBrokerId);
    this.filtersApplied.emit(resetFiltersWithBrokerId);
  }

  loadUnitTypes(): void {
    this.propertyService.getUnitTypes().subscribe({
      next: (response) => {
        this.unitTypes = Object.entries(response.data).map(([key, value]) => ({
          key,
          value: value as string,
        }));
        console.log('Raw API Response:', this.unitTypes);
      },
      error: (err) => {
        console.error('Error loading unitTypes:', err);
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

   loadAreas(cityId?: number): void {
    this.propertyService.getAreas(cityId).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.areas = response.data;
        } else {
          console.warn('No areas data in response');
          this.areas = [];
        }
      },
      error: (err) => {
        console.error('Error loading areas:', err);
        this.areas = [];
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }
}
