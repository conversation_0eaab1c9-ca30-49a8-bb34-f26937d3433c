import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ClientRequestComponent } from './client-request/client-request.component';
import { RouterModule } from '@angular/router';
import { NgApexchartsModule } from 'ng-apexcharts';
import { SharedModule } from 'src/app/_metronic/shared/shared.module';
import { DropdownMenusModule } from 'src/app/_metronic/partials';
import { BrokerModule } from '../../broker/broker.module';
import { RequestsModule } from '../../requests/requests.module';
import { FormsModule } from '@angular/forms';
import { PermissionGuard } from 'src/app/core/guards/permission.guard';

@NgModule({
  declarations: [ClientRequestComponent],

  imports: [
    RouterModule.forChild([
      { path: 'requset',
        canActivate: [PermissionGuard],
        data: { permission: 'list_requests' },
        component: ClientRequestComponent },
    ]),

    NgApexchartsModule,
    SharedModule,
    CommonModule,
    DropdownMenusModule,
    BrokerModule,
    RequestsModule,
    FormsModule,
  ],
})
export class ClientModule {}
