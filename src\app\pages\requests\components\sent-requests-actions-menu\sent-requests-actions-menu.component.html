<div class="position-relative">
  <button
    type="button"
    class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
    [attr.data-kt-menu-trigger]="'click'"
    [attr.data-kt-menu-placement]="'bottom-end'"
    [attr.data-kt-menu-flip]="'top-end'"
    [attr.id]="'menu-trigger-' + requestId"
  >
    <i class="fa-solid fa-ellipsis-vertical"></i>
  </button>

  <div
    class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold w-200px"
    [attr.data-kt-menu]="'true'"
    [attr.id]="'menu-' + requestId"
  >
    <div class="menu-item px-3">
      <div
        class="menu-content fs-6 fw-bolder px-3 py-4 text-dark-blue text-start"
      >
        Actions
      </div>
    </div>

    <div class="separator mb-3 opacity-75"></div>

    <div class="menu-item px-3">
      <a
        class="menu-link px-3 cursor-pointer text-hover-dark-blue"
        [routerLink]="['/requests/render', requestId]"
      >
        <i class="fa-solid fa-eye me-1"></i>
        View
      </a>
    </div>

    <div class="menu-item px-3">
      <a
        class="menu-link px-3 cursor-pointer text-hover-dark-blue"
        [routerLink]="['/requests/assign-to', requestId]"
      >
        <i class="fa-solid fa-share me-1"></i>
        Assign to broker(s)
      </a>
    </div>
    <div class="separator mt-3 opacity-75"></div>
    <div class="menu-item px-3">
      <div class="menu-content px-3 py-3">
        <a class="btn btn-danger btn-sm px-4 cursor-pointer">
          <i class="fa-regular fa-eye-slash"></i>
          Archive
        </a>
      </div>
    </div>
  </div>
</div>
