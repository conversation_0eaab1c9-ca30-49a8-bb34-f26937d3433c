<!-- All Properties Page -->
<div class="container-fluid py-5 px-3 px-md-4 px-lg-5">
  <!-- Simple Header Section -->
  <div class="simple-header mb-4">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <h1 class="h2 mb-2 text-dark">{{ 'HOME.ALL_PROPERTIES.TITLE' | translate }}</h1>
        <p class="text-muted mb-0">{{ 'HOME.ALL_PROPERTIES.SUBTITLE' | translate }}</p>
      </div>
      <button class="btn btn-outline-secondary" (click)="goBack()">
        <i class="fas fa-arrow-left me-2"></i>
        {{ 'HOME.ALL_PROPERTIES.BACK_TO_HOME' | translate }}
      </button>
    </div>
  </div>

  <!-- Search Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-center">
        <div class="search-container" style="max-width: 500px; width: 100%;">
          <div class="input-group input-group-lg">
            <span class="input-group-text bg-white border-end-0">
              <i class="fas fa-search text-muted"></i>
            </span>
            <input type="text" class="form-control border-start-0 ps-0"
              [placeholder]="'HOME.ALL_PROPERTIES.SEARCH_PLACEHOLDER' | translate" [(ngModel)]="searchText"
              (input)="onSearchChange()" style="box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border-radius: 0 8px 8px 0;">
          </div>
        </div>
      </div>
    </div>
  </div>


  <!-- Loading Spinner -->
  <div class="row" *ngIf="isLoading && properties.length === 0">
    <div class="col-12 text-center py-5">
      <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
        <span class="visually-hidden">{{ 'HOME.ALL_PROPERTIES.LOADING_MORE' | translate }}</span>
      </div>
      <p class="mt-3 text-muted">{{ 'HOME.PROPERTIES.LOADING' | translate }}</p>
    </div>
  </div>

  <!-- Properties Grid -->
  <div class="row g-3 g-md-4" *ngIf="filteredProperties.length > 0">
    <div class="col-xl-3 col-lg-4 col-md-6 col-sm-12" *ngFor="let property of filteredProperties; let i = index">
      <div class="property-card h-100" (click)="onPropertyClick(property)" style="cursor: pointer;">
        <div class="property-image position-relative">
          <img [src]="getPropertyImage(property)" [alt]="'Property ' + (i + 1)" class="img-fluid w-100"
            style="height: 250px; object-fit: cover; border-radius: 12px 12px 0 0;">

          <!-- Property Badge -->
          <div class="property-badge position-absolute top-0 start-0 m-3">
            <span class="badge bg-primary fs-6 px-3 py-2">
              {{ getTranslatedOperation(property.unitOperation) }}
            </span>
          </div>

          <!-- Property Location -->
          <div class="property-location position-absolute bottom-0 start-0 m-3">
            <div class="bg-dark bg-opacity-75 text-white px-3 py-2 rounded">
              <i class="fas fa-map-marker-alt me-2"></i>
              <span>{{ getPropertyLocation(property).slice(0, 15) }}</span>
            </div>
          </div>
        </div>

        <div class="property-content p-4">
          <!-- Property Title -->
          <h4 class="property-title fw-bold mb-3 text-truncate">
            {{ getPropertyType(property) | uppercase }}
          </h4>

          <!-- Property Details -->
          <div class="property-details mb-3">
            <div class="row g-2">
              <div class="col-6" *ngIf="property.compoundType">
                <small class="text-muted d-block">{{ 'HOME.PROPERTIES.TYPE' | translate }}</small>
                <span class="fw-semibold">{{ getTranslatedCompoundType(property.compoundType) }}</span>
              </div>
              <div class="col-6" *ngIf="property.numberOfRooms">
                <small class="text-muted d-block">{{ 'HOME.PROPERTIES.ROOMS' | translate }}</small>
                <span class="fw-semibold">{{ property.numberOfRooms }}</span>
              </div>
              <div class="col-6" *ngIf="property.unitArea">
                <small class="text-muted d-block">{{ 'HOME.PROPERTIES.AREA' | translate }}</small>
                <span class="fw-semibold">{{ getFormattedArea(property.unitArea) }}</span>
              </div>
              <div class="col-6" *ngIf="property.numberOfBathrooms">
                <small class="text-muted d-block">{{ 'HOME.PROPERTIES.BATHROOMS' | translate }}</small>
                <span class="fw-semibold">{{ property.numberOfBathrooms }}</span>
              </div>
            </div>
          </div>

          <!-- Property Price -->
          <div class="property-price">
            <h5 class="text-primary fw-bold mb-0">
              {{ formatPrice(property) }}
            </h5>
          </div>

          <!-- View Details Button -->
          <div class="mt-3">
            <button class="btn btn-outline-primary w-100">
              <i class="fas fa-eye me-2"></i>
              {{ 'HOME.ALL_PROPERTIES.VIEW_DETAILS' | translate }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Load More Button -->
  <div class="row mt-5" *ngIf="hasMoreProperties && !isLoading">
    <div class="col-12 text-center">
      <button class="btn btn-primary btn-lg px-5" (click)="loadMoreProperties()">
        <i class="fas fa-plus me-2"></i>
        {{ 'HOME.ALL_PROPERTIES.LOAD_MORE' | translate }}
      </button>
    </div>
  </div>

  <!-- Loading More Spinner -->
  <div class="row mt-3" *ngIf="isLoading && properties.length > 0">
    <div class="col-12 text-center">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">{{ 'HOME.ALL_PROPERTIES.LOADING_MORE' | translate }}</span>
      </div>
    </div>
  </div>

  <!-- No More Properties Message -->
  <div class="row mt-5" *ngIf="!hasMoreProperties && properties.length > 0">
    <div class="col-12 text-center">
      <div class="alert alert-success">
        <i class="fas fa-check-circle me-2"></i>
        {{ 'HOME.ALL_PROPERTIES.ALL_LOADED' | translate }}
      </div>
    </div>
  </div>

  <!-- No Properties Found -->
  <div class="row" *ngIf="!isLoading && filteredProperties.length === 0 && properties.length > 0">
    <div class="col-12 text-center py-5">
      <div class="empty-state">

        <h3 class="text-muted">{{ 'HOME.ALL_PROPERTIES.NO_PROPERTIES_FOUND' | translate }}</h3>
        <p class="text-muted">{{ 'HOME.ALL_PROPERTIES.NO_PROPERTIES_MESSAGE' | translate }}</p>

      </div>
    </div>
  </div>

  <!-- No Properties Loaded -->
  <div class="row" *ngIf="!isLoading && properties.length === 0">
    <div class="col-12 text-center py-5">
      <div class="empty-state">
        <i class="fas fa-home fa-5x text-muted mb-4"></i>
        <h3 class="text-muted">{{ 'HOME.ALL_PROPERTIES.NO_PROPERTIES_AVAILABLE' | translate }}</h3>
        <p class="text-muted">{{ 'HOME.ALL_PROPERTIES.NO_PROPERTIES_AVAILABLE_MESSAGE' | translate }}</p>
        <button class="btn btn-primary mt-3" (click)="goBack()">
          <i class="fas fa-arrow-left me-2"></i>
          {{ 'HOME.ALL_PROPERTIES.BACK_TO_HOME' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>