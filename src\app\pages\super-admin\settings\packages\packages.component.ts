import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { SubscriptionService } from 'src/app/pages/broker/services/subscription.service';
import { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-subscriptions',
  templateUrl: './packages.component.html',
  styleUrls: ['./packages.component.scss']
})
export class SubscriptionsComponent extends BaseGridComponent implements OnInit {

  constructor(private router: Router, protected cd : ChangeDetectorRef, private subscriptionService : SubscriptionService) {

    super(cd);
    this.setService(subscriptionService);
    this.orderBy = 'id';
    this.orderDir = 'DESC';
  }

  createSubscription(): void {
    this.router.navigate(['/super-admin/subscriptions/create']);
  }

  editSubscription(id: number): void {
    this.router.navigate(['/super-admin/subscriptions/edit', id]);
  }

  deleteSubscription(id: number): void {
    Swal.fire({
    title: 'هل أنت متأكد؟',
    text: 'لن يمكنك التراجع عن هذه العملية!',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'نعم، احذف!',
    cancelButtonText: 'إلغاء'
  }).then((result) => {
    if (result.isConfirmed) {
      this.subscriptionService.delete(id).subscribe({
        next: () => {
           Swal.fire('تم الحذف!', 'تم حذف الاشتراك بنجاح.', 'success');
          this.cd.detectChanges();
          document.location.reload();
        },
        error: (err) => {
          Swal.fire('خطأ!', 'حدث خطأ أثناء الحذف.', 'error');
          console.error('Delete error:', err);
        }
      });
    }
  });
  }
}
