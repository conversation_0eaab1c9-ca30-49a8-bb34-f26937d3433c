import { Injectable } from "@angular/core";

@Injectable({
  providedIn: 'root',
})

export abstract class PermissionService  {
  constructor() {}

  /**
   * Check if the current user has a specific permission.
   * @param permissionName The name of the permission to check.
   * @returns true if the permission exists, false otherwise.
   */
  hasPermission(permissionName: string): boolean {
    const user = JSON.parse(localStorage.getItem('currentUser') || '{}');

    if (user && Array.isArray(user.permissions)) {
      return user.permissions.includes(permissionName);
    }

    return false;
  }
}
