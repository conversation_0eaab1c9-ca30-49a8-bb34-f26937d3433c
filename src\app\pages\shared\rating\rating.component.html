<div class="rating-container">
  <h2 class="rating-title">{{ title }}</h2>

  <div class="rating-form">
    <div class="rating-row">

      <div class="stars-container">
        <span *ngFor="let star of getStars()" class="star" [class.filled]="isStarFilled('responseSpeed', star)"
          (click)="setRating('responseSpeed', star)">
          ⭐
        </span>
      </div>
      <label class="rating-label">{{ ratingLabels.responseSpeed }}</label>
    </div>

    <div class="rating-row">

      <div class="stars-container">
        <span *ngFor="let star of getStars()" class="star" [class.filled]="isStarFilled('communicationStyle', star)"
          (click)="setRating('communicationStyle', star)">
          ⭐
        </span>
      </div>
      <label class="rating-label">{{ ratingLabels.communicationStyle }}</label>
    </div>

    <div class="rating-row">

      <div class="stars-container">
        <span *ngFor="let star of getStars()" class="star" [class.filled]="isStarFilled('requestUnderstanding', star)"
          (click)="setRating('requestUnderstanding', star)">
          ⭐
        </span>
      </div>
      <label class="rating-label">{{ ratingLabels.requestUnderstanding }}</label>
    </div>

    <div class="rating-row">

      <div class="stars-container">
        <span *ngFor="let star of getStars()" class="star" [class.filled]="isStarFilled('projectExecution', star)"
          (click)="setRating('projectExecution', star)">
          ⭐
        </span>
      </div>
      <label class="rating-label">{{ ratingLabels.projectExecution }}</label>
    </div>

    <button *ngIf="showSubmitButton" class="submit-button" (click)="submitRating()" [disabled]="!isFormValid()">
      Submit Rating
    </button>


  </div>
</div>