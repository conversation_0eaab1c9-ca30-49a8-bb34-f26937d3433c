// RTL Support for Sidebar Menu
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]) {
  // Fix menu items alignment
  .menu {
    direction: rtl;
    text-align: right;
  }

  .menu-item {
    .menu-link {
      text-align: right;
      justify-content: flex-end;

      .menu-icon {
        margin-left: 0.75rem;
        margin-right: 0;
      }

      .menu-title {
        text-align: right;
      }
    }
  }

  // Fix submenu positioning
  .menu-sub {
    direction: rtl;
    text-align: right;

    .menu-item {
      .menu-link {
        padding-right: 2.5rem;
        padding-left: 1rem;
      }
    }
  }

  // Mobile specific fixes
  @media (max-width: 991.98px) {
    .menu {
      padding-right: 1rem;
      padding-left: 1rem;
    }

    .menu-item {
      .menu-link {
        .menu-icon {
          margin-left: 0.5rem;
          margin-right: 0;
        }
      }
    }
  }
}