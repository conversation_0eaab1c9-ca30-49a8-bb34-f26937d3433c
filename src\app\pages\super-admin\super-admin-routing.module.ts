import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SuperAdminDashboardComponent } from './super-admin-dashboard/super-admin-dashboard.component';
import { SettingsComponent } from './settings/settings.component';
import { SubscriptionsComponent } from './settings/packages/packages.component';
import { UserSettingsComponent } from './settings/user-settings/user-settings.component';
import { SubscriptionFormComponent } from './settings/subscription-form/subscription-form.component';

// Dashboard Components
import { GeneralDashboardComponent } from './super-admin-dashboard/components/general-dashboard/general-dashboard.component';
import { DevelopersDashboardComponent } from './super-admin-dashboard/components/developers-dashboard/developers-dashboard.component';
import { BrokersDashboardComponent } from './super-admin-dashboard/components/brokers-dashboard/brokers-dashboard.component';
import { ClientsDashboardComponent } from './super-admin-dashboard/components/clients-dashboard/clients-dashboard.component';

// User Account Types Components
import { UserAccountTypesComponent } from './settings/user-account-types/user-account-types.component';
import { UsersComponent } from './settings/user-account-types/users/users.component';
import { PermissionsComponent } from './settings/user-account-types/permissions/permissions.component';
import { RolesComponent } from './settings/user-account-types/roles/roles.component';

// Table Components
import { AllDevelopersComponent } from './all-developers/all-developers.component';
import { DeveloperDetailsComponent } from './all-developers/developer-details/developer-details.component';
import { AllBrokersComponent } from './all-brokers/all-brokers.component';
import { BrokerDetailsComponent } from './all-brokers/broker-details/broker-details.component';
import { AllUsersComponent } from './all-users/all-users.component';
import { UserDetailsComponent } from './all-users/user-details/user-details.component';
import { PermissionGuard } from 'src/app/core/guards/permission.guard';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full'
  },
  {
    path: 'dashboard',
    component: SuperAdminDashboardComponent
  },
  // Dashboard Routes
  {
    path: 'dashboard/general',
    component: GeneralDashboardComponent
  },
  {
    path: 'dashboard/developers',
    component: DevelopersDashboardComponent
  },
  {
    path: 'dashboard/brokers',
    component: BrokersDashboardComponent
  },
  {
    path: 'dashboard/clients',
    component: ClientsDashboardComponent
  },
  {
    path: 'settings',
    canActivate: [PermissionGuard],
    data: { permission: 'admin_settings' },
    component: SettingsComponent
  },
  {
    path: 'subscriptions',
    canActivate: [PermissionGuard],
    data: { permission: 'admin_subscriptions' },
    component: SubscriptionsComponent
  },
  {
    path: 'subscriptions/create',
    component: SubscriptionFormComponent
  },
  {
    path: 'subscriptions/edit/:id',
    component: SubscriptionFormComponent
  },
  {
    path: 'user-settings',
    component: UserSettingsComponent
  },
  // User Account Types Routes
  {
    path: 'user-account-types',
    component: UserAccountTypesComponent
  },
  {
    path: 'user-account-types/users',
    component: UsersComponent
  },
  {
    path: 'user-account-types/permissions',
    component: PermissionsComponent
  },
  {
    path: 'user-account-types/roles',
    component: RolesComponent
  },
  // Table Routes
  {
    path: 'all-developers',
    component: AllDevelopersComponent
  },
  {
    path: 'all-developers/details',
    component: DeveloperDetailsComponent
  },
  {
    path: 'all-brokers',
    component: AllBrokersComponent
  },
  {
    path: 'all-brokers/broker-details',
    component: BrokerDetailsComponent
  },
  {
    path: 'all-users',
    component: AllUsersComponent
  },
  {
    path: 'all-users/user-details',
    component: UserDetailsComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SuperAdminRoutingModule { }
