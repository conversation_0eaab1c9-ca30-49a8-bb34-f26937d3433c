import { Injectable } from "@angular/core";
import { AbstractCrudService } from "../../shared/services/abstract-crud.service";
import { environment } from "src/environments/environment";
import { Observable } from "rxjs";

@Injectable({
    providedIn: 'root',
})

export class SubscriptionService extends AbstractCrudService {

  apiUrl = `${environment.apiUrl}/broker/account/types`;


  update(id: number, model: any): Observable<any> {

    return this.http.post(this.apiUrl + '/update/' + id, model);
  }

}
