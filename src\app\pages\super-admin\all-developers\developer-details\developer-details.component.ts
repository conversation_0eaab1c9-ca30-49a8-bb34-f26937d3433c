import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DevelopersService } from '../../services/developers.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-developer-details',
  templateUrl: './developer-details.component.html',
  styleUrls: ['./developer-details.component.scss']
})
export class DeveloperDetailsComponent implements OnInit {
  developer: any = null;

  developerId: number;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private developersService: DevelopersService,
    private cd: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.developerId = params['developerId'];
      if (this.developerId) {
        this.loadDeveloperDetails();
      } else {
        this.router.navigate(['/super-admin/all-developers']);
      }
    });
  }

  loadDeveloperDetails(): void {


    this.developersService.getDeveloperById(this.developerId).subscribe({
      next: (response) => {
        console.log('Developer details:', response);
        this.developer = response.data ;
        this.cd.detectChanges();

      },
      error: (error) => {
        console.error('Error loading developer details:', error);

        Swal.fire('Error', 'Failed to load developer details. Please try again.', 'error');
        this.router.navigate(['/super-admin/all-developers']);
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/super-admin/all-developers']);
  }

  getInitials(name: string): string {
    if (!name) return '';
    return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase();
  }

  getStatusClass(status: boolean): string {
    return status ? 'badge-light-success' : 'badge-light-danger';
  }

  getStatusText(status: boolean): string {
    return status ? 'Active' : 'Inactive';
  }

  toggleDeveloperStatus(): void {
    if (!this.developer) return;

    const action = this.developer.isActive ? 'suspend' : 'activate';
    const message = this.developer.isActive ? 'suspend this developer account?' : 'activate this developer account?';

    Swal.fire({
      title: 'Are you sure?',
      text: `Do you want to ${message}`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: `Yes, ${action}!`
    }).then((result) => {
      if (result.isConfirmed) {
        this.developersService.toggleDeveloperStatus(this.developer.id).subscribe({
          next: (response) => {
            console.log('Status toggled successfully:', response);
            this.developer.isActive = !this.developer.isActive;
            Swal.fire(
              'Success!',
              `Developer account has been ${this.developer.isActive ? 'activated' : 'suspended'}.`,
              'success'
            ).then(() => {
              // Refresh the page after success message
              window.location.reload();
            });
          },
          error: (error) => {
            console.error('Error toggling status:', error);
            Swal.fire('Error', 'Failed to update developer status. Please try again.', 'error');
          }
        });
      }
    });
  }
}
