<div class="d-flex align-items-center mb-10" *ngFor="let history of requestHistory">
  <span *ngIf="checkAssign(history.status)">
    <span *ngIf="history.status === 'Create'" class="bullet bullet-vertical h-40px bg-danger me-5"></span>
    <span *ngIf="history.status === 'Assign'" class="bullet bullet-vertical h-40px bg-primary me-5"></span>
    <span *ngIf="history.status === 'Update_status'" class="bullet bullet-vertical h-40px bg-warning me-5"></span>
    <span *ngIf="history.status === 'Reply'" class="bullet bullet-vertical h-40px bg-success me-5"></span>
  </span>

  <div class="flex-grow-1" *ngIf="checkAssign(history.status)">
    <span class="text-gray-800 fw-bolder fs-5">
      {{ history.description }}
    </span>
    <span class="text-muted fw-bold d-block"> {{ history.createdAt | date: 'fullDate' }} </span>
  </div>

  <span *ngIf="checkAssign(history.status)">
    <span *ngIf="history.status === 'Create'" class="badge badge-light-danger fs-6 fw-bolder">{{ history.status
      }}</span>
    <span *ngIf="history.status === 'Assign'" class="badge badge-light-primary fs-6 fw-bolder">{{ history.status
      }}</span>
    <span *ngIf="history.status === 'Update_status'" class="badge badge-light-warning fs-6 fw-bolder">{{ history.status
      }}</span>
    <span *ngIf="history.status === 'Reply'" class="badge badge-light-success fs-6 fw-bolder">{{ history.status
      }}</span>
  </span>
</div>
