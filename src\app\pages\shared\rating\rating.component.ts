import { Component, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import Swal from 'sweetalert2';
import { BrokerService } from '../../broker/services/broker.service';

export interface RatingData {
  responseSpeed: number;
  communicationStyle: number;
  requestUnderstanding: number;
  projectExecution: number;
}

@Component({
  selector: 'app-rating',
  templateUrl: './rating.component.html',
  styleUrls: ['./rating.component.scss']
})
export class RatingComponent {
  @Input() title: string = 'Feedback Form';
  @Input() showSubmitButton: boolean = true;
  @Input() brokerId: number;
  @Output() ratingSubmitted = new EventEmitter<RatingData>();

  userId: number;

  ratings: RatingData = {
    responseSpeed: 0,
    communicationStyle: 0,
    requestUnderstanding: 0,
    projectExecution: 0
  };

  ratingLabels = {
    responseSpeed: 'Response Speed',
    communicationStyle: 'Communication Style',
    requestUnderstanding: 'Request Understanding',
    projectExecution: 'Project Execution'
  };

  constructor( protected cd: ChangeDetectorRef, protected brokerService: BrokerService)
    {
      this.userId = JSON.parse(localStorage.getItem('currentUser')!).id;
    }

  setRating(category: keyof RatingData, rating: number): void {
    this.ratings[category] = rating;
  }

  getStars(): number[] {
    return [1, 2, 3, 4, 5];
  }

  isStarFilled(category: keyof RatingData, starNumber: number): boolean {
    return this.ratings[category] >= starNumber;
  }

  submitRating(): void {
    const totalRating = this.calculateTotalRating();
    this.ratingSubmitted.emit(this.ratings);

    const payload = {
      ratedId: this.brokerId,
      rate: totalRating
    };

    this.brokerService.rateBroker(this.userId, payload).subscribe({
      next: (response) => {
        Swal.fire('Rating submitted!', '', 'success');
      },
      error: (error) => {
        Swal.fire('Failed to submit rating.', '', 'error');
        console.error(error);
      }
    });

  }

  calculateTotalRating(): number {
    const ratings = [
      this.ratings.responseSpeed,
      this.ratings.communicationStyle,
      this.ratings.requestUnderstanding,
      this.ratings.projectExecution
    ];

    const sum = ratings.reduce((total, rating) => total + rating, 0);
    const average = sum / ratings.length;
    return Math.round(average * 100) / 100;
  }

  isFormValid(): boolean {
    return Object.values(this.ratings).every(rating => rating > 0);
  }
}
