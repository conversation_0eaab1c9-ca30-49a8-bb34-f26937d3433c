<div class="container-fluid px-4 py-6">
  <!-- Header -->
  <div class="row mb-6">
    <div class="col-12">
      <div class="d-flex align-items-center justify-content-between">
        <div>
          <h1 class="text-dark-blue fw-bold mb-2">
            <i class="fas fa-hard-hat me-3"></i>
            بيانات المطورين
          </h1>
          <p class="text-muted mb-0">إحصائيات شاملة للمطورين والمشاريع ومتوسط مدة السداد</p>
        </div>
        <button class="btn btn-light-primary" (click)="loadDeveloperStatistics()">
          <i class="fas fa-sync-alt me-2"></i>
          تحديث البيانات
        </button>
      </div>
    </div>
  </div>

  <!-- Main Statistics Cards -->
  <div class="row g-6 mb-8">
    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100 bg-primary">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-users fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ formatNumber(totalDevelopers) }}</div>
              <div class="fs-7 text-white-75">إجمالي المطورين</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100 bg-dark-blue">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-user-check fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ formatNumber(activeDevelopers) }}</div>
              <div class="fs-7 text-white-75">المطورين النشطين</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100 bg-mid-blue">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-building fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ formatNumber(totalDeveloperUnits) }}</div>
              <div class="fs-7 text-white-75">إجمالي الوحدات</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100 bg-success">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-calendar-alt fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ averagePaymentPeriod }}</div>
              <div class="fs-7 text-white-75">متوسط مدة السداد (شهر)</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Top Developers and Revenue Stats -->
  <div class="row g-6 mb-8">
    <!-- Top Developers -->
    <div class="col-lg-8">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-trophy me-2"></i>
            أفضل المطورين
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">المطور</th>
                  <th>المشاريع</th>
                  <th>الوحدات</th>
                  <th>الإيرادات</th>
                  <th class="rounded-end">التقييم</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let developer of topDevelopers; let i = index">
                  <td class="ps-4">
                    <div class="d-flex align-items-center">
                      <div class="symbol symbol-40px me-3">
                        <div class="symbol-label bg-light-primary">
                          <span class="text-primary fw-bold">{{ i + 1 }}</span>
                        </div>
                      </div>
                      <div>
                        <span class="text-dark fw-bold">{{ developer.name }}</span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="badge badge-light-primary">{{ developer.projects }}</span>
                  </td>
                  <td>
                    <span class="text-muted">{{ formatNumber(developer.units) }}</span>
                  </td>
                  <td>
                    <span class="text-success fw-bold">{{ formatPrice(developer.revenue) }}</span>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <span class="me-2">{{ developer.rating }}</span>
                      <div class="rating">
                        <i *ngFor="let star of getRatingStars(developer.rating)" [class]="star"></i>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Revenue Statistics -->
    <div class="col-lg-4">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-chart-line me-2"></i>
            إحصائيات الإيرادات
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="row g-4">
            <div class="col-12">
              <div class="bg-light-success p-4 rounded">
                <div class="text-success fs-6 fw-bold">إجمالي الإيرادات</div>
                <div class="text-dark fs-3 fw-bolder">{{ formatPrice(revenueStats.totalRevenue) }}</div>
              </div>
            </div>
            <div class="col-12">
              <div class="bg-light-primary p-4 rounded">
                <div class="text-primary fs-6 fw-bold">الإيرادات الشهرية</div>
                <div class="text-dark fs-3 fw-bolder">{{ formatPrice(revenueStats.monthlyRevenue) }}</div>
              </div>
            </div>
            <div class="col-12">
              <div class="bg-light-warning p-4 rounded">
                <div class="text-warning fs-6 fw-bold">متوسط قيمة المشروع</div>
                <div class="text-dark fs-3 fw-bolder">{{ formatPrice(revenueStats.averageProjectValue) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Best Selling Projects -->
  <div class="row g-6 mb-8">
    <div class="col-12">
      <div class="card card-flush">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-medal me-2"></i>
            المشاريع الأكثر مبيعاً
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">المشروع</th>
                  <th>المطور</th>
                  <th>الموقع</th>
                  <th>إجمالي الوحدات</th>
                  <th>الوحدات المباعة</th>
                  <th>متوسط السعر</th>
                  <th>نسبة المبيعات</th>
                  <th class="rounded-end">تاريخ التسليم</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let project of bestSellingProjects">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ project.name }}</span>
                  </td>
                  <td>
                    <span class="text-muted">{{ project.developer }}</span>
                  </td>
                  <td>
                    <span class="text-muted">{{ project.location }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-info">{{ formatNumber(project.totalUnits) }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-success">{{ formatNumber(project.soldUnits) }}</span>
                  </td>
                  <td>
                    <span class="text-primary fw-bold">{{ formatPrice(project.averagePrice) }}</span>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="progress h-6px w-100px me-2">
                        <div class="progress-bar bg-success" [style.width.%]="project.salesPercentage"></div>
                      </div>
                      <span class="text-muted fs-7">{{ project.salesPercentage }}%</span>
                    </div>
                  </td>
                  <td>
                    <span class="text-muted">{{ project.completionDate | date:'MMM yyyy' }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Payment Period Stats and Project Status -->
  <div class="row g-6 mb-8">
    <!-- Payment Period Statistics -->
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-clock me-2"></i>
            إحصائيات مدة السداد
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">مدة السداد</th>
                  <th>عدد المشاريع</th>
                  <th class="rounded-end">النسبة</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let period of paymentPeriodStats">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ period.period }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-primary">{{ period.count }}</span>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="progress h-6px w-100px me-2">
                        <div class="progress-bar bg-primary" [style.width.%]="period.percentage"></div>
                      </div>
                      <span class="text-muted fs-7">{{ period.percentage }}%</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Project Status Statistics -->
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-tasks me-2"></i>
            حالة المشاريع
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="row g-4">
            <div class="col-6">
              <div class="bg-light-success p-4 rounded text-center">
                <div class="text-success fs-6 fw-bold">مكتملة</div>
                <div class="text-dark fs-2 fw-bolder">{{ projectStatusStats.completed }}</div>
              </div>
            </div>
            <div class="col-6">
              <div class="bg-light-primary p-4 rounded text-center">
                <div class="text-primary fs-6 fw-bold">تحت الإنشاء</div>
                <div class="text-dark fs-2 fw-bolder">{{ projectStatusStats.underConstruction }}</div>
              </div>
            </div>
            <div class="col-6">
              <div class="bg-light-warning p-4 rounded text-center">
                <div class="text-warning fs-6 fw-bold">في التخطيط</div>
                <div class="text-dark fs-2 fw-bolder">{{ projectStatusStats.planning }}</div>
              </div>
            </div>
            <div class="col-6">
              <div class="bg-light-danger p-4 rounded text-center">
                <div class="text-danger fs-6 fw-bold">متوقفة</div>
                <div class="text-dark fs-2 fw-bolder">{{ projectStatusStats.onHold }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Developer Performance -->
  <div class="row g-6">
    <div class="col-12">
      <div class="card card-flush">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-chart-bar me-2"></i>
            أداء المطورين
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">المطور</th>
                  <th>التسليم في الموعد</th>
                  <th>رضا العملاء</th>
                  <th>تقييم الجودة</th>
                  <th class="rounded-end">سرعة المبيعات</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let performance of developerPerformance">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ performance.name }}</span>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="progress h-6px w-100px me-2">
                        <div class="progress-bar" [ngClass]="'bg-' + getPerformanceColor(performance.onTimeDelivery)"
                          [style.width.%]="performance.onTimeDelivery"></div>
                      </div>
                      <span class="text-muted fs-7">{{ performance.onTimeDelivery }}%</span>
                    </div>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <span class="me-2">{{ performance.customerSatisfaction }}</span>
                      <div class="rating">
                        <i *ngFor="let star of getRatingStars(performance.customerSatisfaction)" [class]="star"></i>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <span class="me-2">{{ performance.qualityRating }}</span>
                      <div class="rating">
                        <i *ngFor="let star of getRatingStars(performance.qualityRating)" [class]="star"></i>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="progress h-6px w-100px me-2">
                        <div class="progress-bar" [ngClass]="'bg-' + getPerformanceColor(performance.salesVelocity)"
                          [style.width.%]="performance.salesVelocity"></div>
                      </div>
                      <span class="text-muted fs-7">{{ performance.salesVelocity }}%</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>