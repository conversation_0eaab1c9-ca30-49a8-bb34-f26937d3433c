<div class="row mb-5">
  <div class="col-12">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-center justify-content-between">
          <div class="d-flex align-items-center">
            <div class="header-icon me-4">
              <div class="icon-wrapper bg-light-dark-blue">
                <span class="menu-icon">
                  <img class="mx-auto h-20px w-20px" src="../../../../../../assets/media/broker/subscription2.png"
                    alt="" />
                </span>
              </div>
            </div>
            <div class="header-content">
              <h3 class="header-title mb-1 text-dark-blue">Subscription Management</h3>
              <p class="header-subtitle mb-0">Manage subscription plans and pricing</p>
            </div>
          </div>

          <button class="btn btn-dark-blue" (click)="createSubscription()">
            <i class="fas fa-plus me-1"></i>
            Create New Subscription
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Subscription Cards -->
<div class="row mt-7">
  <div class="col-md-4 mb-2" *ngFor="let subscription of rows">
    <div class="card card-shadow h-100">
      <div class="card-body d-flex flex-center flex-column py-9 px-13">
        <div class="symbol symbol-65px symbol-circle mb-5">
          <img class="mx-auto h-65px"
              [src]="subscription.image"
              alt="image"
          />
        </div>

        <span class="fs-1 text-dark-blue fw-bold mb-2">{{ subscription.name }}</span>
        <span class="fs-3 text-dark-blue fw-bold mb-5">{{ subscription.price }} EGP <sub>Monthly</sub></span>

        <div class="fw-semibold text-gray-500 mb-6 fs-4 px-13 text-center">
          {{ subscription.description }}
        </div>

        <div class="d-flex text-center mb-7">
          <div class="border border-dashed rounded min-w-50px py-3 px-4 mx-2 mb-3">
            <div class="fs-4 fw-bold text-gray-700">{{ subscription.maxSpecializations }}</div>
            <div class="fw-semibold text-gray-500">Specializations</div>
          </div>

          <div class="border border-dashed rounded min-w-50px py-3 px-4 mx-2 mb-3">
            <div class="fs-4 fw-bold text-gray-700">{{ subscription.maxLocations }}</div>
            <div class="fw-semibold text-gray-500">Locations</div>
          </div>

          <div class="border border-dashed rounded min-w-50px py-3 px-4 mx-2 mb-3">
            <div class="fs-4 fw-bold text-gray-700">{{ subscription.maxAdvertisements }}</div>
            <div class="fw-semibold text-gray-500">Advertisements</div>
          </div>
        </div>

        <div class="d-flex text-center mb-7">
          <div class="border border-dashed rounded min-w-50px py-3 px-4 mx-2 mb-3">
            <div class="fs-4 fw-bold text-gray-700">{{ subscription.maxOperations }}</div>
            <div class="fw-semibold text-gray-500">Operations</div>
          </div>
        </div>

        <div class="d-flex gap-2 w-100">
          <button
            class="btn btn-md fw-bolder btn-dark-blue btn-flex flex-fill text-center justify-content-center btn-center"
            (click)="editSubscription(subscription.id)">
            <i class="fas fa-edit me-2"></i>
            Edit
          </button>
          <button class="btn btn-md fw-bolder btn-danger btn-flex btn-center justify-content-center flex-fill"
            (click)="deleteSubscription(subscription.id)">
            <i class="fas fa-trash me-2"></i>
            Delete
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
